#!/usr/bin/env python3
"""
Comprehensive test to fill ALL 188 fields in the PDF including checkboxes, prices, amounts, and initials
"""

import requests
import json
from datetime import datetime, timedelta

# Configuration
BACKEND_URL = "http://localhost:5000"

def create_pdf_fields_file():
    """Create the pdf_fields.json file from extracted PDF data"""
    try:
        response = requests.get(f"{BACKEND_URL}/extract-fields")
        if response.status_code == 200:
            data = response.json()
            pdf_fields = {}

            for field_name, field_info in data['fields'].items():
                # Convert to the format expected by our system
                pdf_fields[f"'({field_name})'"] = {
                    "name": f"'({field_name})'",
                    "type": f"/{field_info['type']}",
                    "current_value": "()" if field_info['type'] == 'Tx' else "",
                    "page": field_info.get('page', 1)
                }

            # Save to file
            with open('pdf_fields.json', 'w') as f:
                json.dump(pdf_fields, f, indent=2)

            print(f"✅ Created pdf_fields.json with {len(pdf_fields)} fields")
            return True
        else:
            print(f"❌ Failed to extract fields: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error creating PDF fields file: {e}")
        return False

def create_comprehensive_field_mapping():
    """Create comprehensive field mapping for all 188 fields"""

    field_mapping = {
        "fields": {
            # Basic party information (Text 1-7)
            "seller_name": "Text_1",
            "buyer_name": "Text_2",
            "property_address": "Text_3",
            "county": "Text_4",
            "tax_id": "Text_5",
            "legal_description": "Text_6",
            "additional_property_details": "Text_7",

            # Personal property and exclusions (Text 8-11)
            "personal_property_included": "Text_8",
            "personal_property_excluded": "Text_9",
            "fixtures_included": "Text_10",
            "other_items": "Text_11",

            # Contact information
            "seller_phone": "US_Phone_Number_1",
            "seller_email": "Email_1",
            "buyer_phone": "US_Phone_Number_2",

            # Financial terms (Numbers 1-9)
            "purchase_price": "Number_1",
            "initial_deposit": "Number_2",
            "additional_deposit": "Number_3",
            "loan_amount": "Number_4",
            "down_payment": "Number_5",
            "closing_costs": "Number_6",
            "inspection_fee": "Number_7",
            "appraisal_fee": "Number_8",
            "survey_cost": "Number_9",

            # Dates
            "contract_date": "Date_1",
            "closing_date": "Date_2",
            "inspection_deadline": "Date_3",
            "financing_deadline": "Date_4",
            "appraisal_deadline": "Date_5",

            # Additional text fields (Text 12-58)
            "seller_attorney": "Text_12",
            "buyer_attorney": "Text_13",
            "title_company": "Text_14",
            "real_estate_agent_seller": "Text_15",
            "real_estate_agent_buyer": "Text_16",
            "lender_name": "Text_17",
            "loan_officer": "Text_18",
            "appraiser": "Text_19",
            "inspector": "Text_20",
            "surveyor": "Text_21",
            "insurance_agent": "Text_22",
            "hoa_management": "Text_23",
            "utilities_electric": "Text_24",
            "utilities_gas": "Text_25",
            "utilities_water": "Text_26",
            "utilities_cable": "Text_27",
            "utilities_internet": "Text_28",
            "utilities_trash": "Text_29",
            "utilities_security": "Text_30",
            "maintenance_pool": "Text_31",
            "maintenance_landscape": "Text_32",
            "maintenance_hvac": "Text_33",
            "maintenance_dock": "Text_34",
            "special_assessments": "Text_35",
            "hoa_fees": "Text_36",
            "property_taxes": "Text_37",
            "insurance_cost": "Text_38",
            "utility_costs": "Text_39",
            "maintenance_costs": "Text_40",
            "rental_restrictions": "Text_41",
            "pet_restrictions": "Text_42",
            "parking_spaces": "Text_43",
            "storage_areas": "Text_44",
            "outdoor_features": "Text_45",
            "recent_improvements": "Text_46",
            "warranty_information": "Text_47",
            "disclosure_items": "Text_48",
            "additional_terms": "Text_49",
            "contingencies": "Text_50",
            "special_conditions": "Text_51",
            "possession_date": "Text_52",
            "key_information": "Text_53",
            "alarm_codes": "Text_54",
            "utility_transfers": "Text_55",
            "final_walkthrough": "Text_56",
            "document_delivery": "Text_57",
            "communication_preferences": "Text_58",

            # Buyer initials (Initials 1-18)
            "buyer_initial_1": "Initials_1",
            "buyer_initial_2": "Initials_2",
            "buyer_initial_3": "Initials_3",
            "buyer_initial_4": "Initials_4",
            "buyer_initial_5": "Initials_5",
            "buyer_initial_6": "Initials_6",
            "buyer_initial_7": "Initials_7",
            "buyer_initial_8": "Initials_8",
            "buyer_initial_9": "Initials_9",
            "buyer_initial_10": "Initials_10",
            "buyer_initial_11": "Initials_11",
            "buyer_initial_12": "Initials_12",
            "buyer_initial_13": "Initials_13",
            "buyer_initial_14": "Initials_14",
            "buyer_initial_15": "Initials_15",
            "buyer_initial_16": "Initials_16",
            "buyer_initial_17": "Initials_17",
            "buyer_initial_18": "Initials_18",

            # Seller initials (Initials 19-36)
            "seller_initial_1": "Initials_19",
            "seller_initial_2": "Initials_20",
            "seller_initial_3": "Initials_21",
            "seller_initial_4": "Initials_22",
            "seller_initial_5": "Initials_23",
            "seller_initial_6": "Initials_24",
            "seller_initial_7": "Initials_25",
            "seller_initial_8": "Initials_26",
            "seller_initial_9": "Initials_27",
            "seller_initial_10": "Initials_28",
            "seller_initial_11": "Initials_29",
            "seller_initial_12": "Initials_30",
            "seller_initial_13": "Initials_31",
            "seller_initial_14": "Initials_32",
            "seller_initial_15": "Initials_33",
            "seller_initial_16": "Initials_34",
            "seller_initial_17": "Initials_35",
            "seller_initial_18": "Initials_36",

            # Signatures
            "buyer_signature_1": "Signature_1",
            "buyer_signature_2": "Signature_2",
            "seller_signature_1": "Signature_3",
            "seller_signature_2": "Signature_4",
        },
        "checkbox_groups": {
            # Financing options
            "financing": {
                "cash": "Checkbox_8",
                "conventional": "Checkbox_9",
                "fha": "Checkbox_10",
                "va": "Checkbox_11",
                "usda": "Checkbox_12",
                "other": "Checkbox_13"
            },

            # Property condition and inspections
            "inspections": {
                "as_is_condition": "Checkbox_1",
                "seller_repairs": "Checkbox_2",
                "buyer_inspection": "Checkbox_3",
                "professional_inspection": "Checkbox_4",
                "termite_inspection": "Checkbox_5",
                "roof_inspection": "Checkbox_6",
                "hvac_inspection": "Checkbox_7",
                "electrical_inspection": "Checkbox_14",
                "plumbing_inspection": "Checkbox_15",
                "pool_inspection": "Checkbox_16",
                "dock_inspection": "Checkbox_17",
                "environmental_inspection": "Checkbox_18",
                "lead_paint_inspection": "Checkbox_19",
                "radon_inspection": "Checkbox_20",
                "mold_inspection": "Checkbox_21",
                "structural_inspection": "Checkbox_22",
                "foundation_inspection": "Checkbox_23",
                "septic_inspection": "Checkbox_24",
                "well_inspection": "Checkbox_25",
                "survey_required": "Checkbox_26",
                "appraisal_required": "Checkbox_27"
            },

            # Included appliances and fixtures
            "appliances": {
                "refrigerator": "Checkbox_28",
                "washer": "Checkbox_29",
                "dryer": "Checkbox_30",
                "dishwasher": "Checkbox_31",
                "microwave": "Checkbox_32",
                "oven_range": "Checkbox_33",
                "garbage_disposal": "Checkbox_34",
                "wine_cooler": "Checkbox_35",
                "ice_maker": "Checkbox_36",
                "security_system": "Checkbox_37",
                "garage_door_openers": "Checkbox_38",
                "pool_equipment": "Checkbox_39",
                "spa_equipment": "Checkbox_40",
                "dock_equipment": "Checkbox_41",
                "outdoor_kitchen": "Checkbox_42",
                "generator": "Checkbox_43",
                "solar_panels": "Checkbox_44",
                "water_softener": "Checkbox_45",
                "reverse_osmosis": "Checkbox_46",
                "central_vacuum": "Checkbox_47",
                "intercom_system": "Checkbox_48",
                "sound_system": "Checkbox_49",
                "lighting_controls": "Checkbox_50",
                "irrigation_system": "Checkbox_51",
                "landscape_lighting": "Checkbox_52",
                "outdoor_speakers": "Checkbox_53",
                "fire_pit": "Checkbox_54",
                "pergola": "Checkbox_55",
                "gazebo": "Checkbox_56"
            },

            # Closing and title
            "closing": {
                "title_insurance": "Checkbox_72",
                "survey_required_closing": "Checkbox_73"
            }
        }
    }

    # Save the mapping
    with open('field_mapping.json', 'w') as f:
        json.dump(field_mapping, f, indent=2)

    print(f"✅ Created comprehensive field mapping with {len(field_mapping['fields'])} fields and {len(field_mapping['checkbox_groups'])} checkbox groups")
    return field_mapping

def create_comprehensive_form_data():
    """Create comprehensive form data that maps to all 188 PDF fields"""

    # Calculate some dates
    today = datetime.now()
    contract_date = today.strftime("%m/%d/%Y")
    closing_date = (today + timedelta(days=45)).strftime("%m/%d/%Y")
    inspection_date = (today + timedelta(days=10)).strftime("%m/%d/%Y")

    return {
        # BASIC PARTY INFORMATION (Text fields 1-7)
        "seller_name": "Sarah Elizabeth Johnson",
        "buyer_name": "Michael Anthony Rodriguez & Maria Carmen Rodriguez",
        "property_address": "1247 Ocean Boulevard, Miami Beach, FL 33139",
        "county": "Miami-Dade County",
        "tax_id": "30-4567-890-1234",
        "legal_description": "Lot 15, Block 3, SUNSET ESTATES SUBDIVISION, according to the plat thereof recorded in Plat Book 45, Page 67, Public Records of Miami-Dade County, Florida",
        "additional_property_details": "Waterfront property with private dock, pool, spa, and three-car garage. Impact windows throughout.",

        # PERSONAL PROPERTY AND EXCLUSIONS (Text fields 8-11)
        "personal_property_included": "All built-in appliances, window treatments, ceiling fans, light fixtures, pool equipment, dock equipment, and landscape lighting",
        "personal_property_excluded": "Seller's personal furniture, artwork, wine collection, and boat lift motor",
        "fixtures_included": "All permanently installed fixtures including chandeliers, built-in sound system, and security cameras",
        "other_items": "Generator, outdoor kitchen appliances, and pool heater",

        # FINANCIAL TERMS (Numbers 1-9)
        "purchase_price": 2750000,  # $2,750,000
        "initial_deposit": 275000,   # $275,000 (10%)
        "additional_deposit": 137500, # $137,500 (5%)
        "loan_amount": 2200000,      # $2,200,000
        "down_payment": 550000,      # $550,000 (20%)
        "closing_costs": 27500,      # $27,500
        "inspection_fee": 850,       # $850
        "appraisal_fee": 1200,       # $1,200
        "survey_cost": 2500,         # $2,500

        # CONTACT INFORMATION
        "seller_phone": "(*************",
        "seller_email": "<EMAIL>",
        "buyer_phone": "(*************",

        # DATES
        "contract_date": contract_date,
        "closing_date": closing_date,
        "inspection_deadline": inspection_date,
        "financing_deadline": (today + timedelta(days=30)).strftime("%m/%d/%Y"),
        "appraisal_deadline": (today + timedelta(days=25)).strftime("%m/%d/%Y"),

        # FINANCING OPTIONS (Checkbox group)
        "financing": {
            "cash": False,
            "conventional": True,
            "fha": False,
            "va": False,
            "usda": False,
            "other": False
        },
        
        # PROPERTY CONDITION AND INSPECTIONS (Checkbox group)
        "inspections": {
            "as_is_condition": True,
            "seller_repairs": False,
            "buyer_inspection": True,
            "professional_inspection": True,
            "termite_inspection": True,
            "roof_inspection": True,
            "hvac_inspection": True,
            "electrical_inspection": True,
            "plumbing_inspection": True,
            "pool_inspection": True,
            "dock_inspection": True,
            "environmental_inspection": False,
            "lead_paint_inspection": False,
            "radon_inspection": False,
            "mold_inspection": True,
            "structural_inspection": True,
            "foundation_inspection": True,
            "septic_inspection": False,
            "well_inspection": False,
            "survey_required": True,
            "appraisal_required": True
        },

        # INCLUDED APPLIANCES AND FIXTURES (Checkbox group)
        "appliances": {
            "refrigerator": True,
            "washer": True,
            "dryer": True,
            "dishwasher": True,
            "microwave": True,
            "oven_range": True,
            "garbage_disposal": True,
            "wine_cooler": True,
            "ice_maker": True,
            "security_system": True,
            "garage_door_openers": True,
            "pool_equipment": True,
            "spa_equipment": True,
            "dock_equipment": True,
            "outdoor_kitchen": True,
            "generator": True,
            "solar_panels": True,
            "water_softener": True,
            "reverse_osmosis": True,
            "central_vacuum": True,
            "intercom_system": True,
            "sound_system": True,
            "lighting_controls": True,
            "irrigation_system": True,
            "landscape_lighting": True,
            "outdoor_speakers": True,
            "fire_pit": True,
            "pergola": True,
            "gazebo": False
        },

        # CLOSING AND TITLE (Checkbox group)
        "closing": {
            "title_insurance": True,
            "survey_required_closing": True
        },
        
        # ADDITIONAL TEXT FIELDS (Text 12-58)
        "seller_attorney": "Johnson, Smith & Associates, P.A.",
        "buyer_attorney": "Rodriguez Legal Group",
        "title_company": "Secure Title & Escrow Services",
        "real_estate_agent_seller": "Premium Properties Realty - Agent: Jennifer Walsh",
        "real_estate_agent_buyer": "Coastal Living Realty - Agent: David Chen",
        "lender_name": "First National Bank of Florida",
        "loan_officer": "Robert Thompson, Senior Loan Officer",
        "appraiser": "Certified Appraisal Services LLC",
        "inspector": "Thorough Home Inspections Inc.",
        "surveyor": "Precision Land Surveying",
        "insurance_agent": "Coastal Insurance Partners",
        "hoa_management": "Sunset Estates Community Management",
        "utilities_electric": "Florida Power & Light",
        "utilities_gas": "Florida City Gas",
        "utilities_water": "Miami-Dade Water & Sewer",
        "utilities_cable": "Xfinity/Comcast",
        "utilities_internet": "AT&T Fiber",
        "utilities_trash": "Waste Management Inc.",
        "utilities_security": "ADT Security Services",
        "maintenance_pool": "Crystal Clear Pool Service",
        "maintenance_landscape": "Tropical Landscape Solutions",
        "maintenance_hvac": "Cool Breeze HVAC Services",
        "maintenance_dock": "Marina Maintenance Pros",
        "special_assessments": "None currently pending",
        "hoa_fees": "$450 monthly",
        "property_taxes": "$28,750 annually",
        "insurance_cost": "$8,500 annually",
        "utility_costs": "Approximately $350 monthly",
        "maintenance_costs": "Pool: $200/month, Landscape: $300/month",
        "rental_restrictions": "No short-term rentals under 6 months",
        "pet_restrictions": "Maximum 2 pets, no weight restrictions",
        "parking_spaces": "3-car garage plus 2 driveway spaces",
        "storage_areas": "Garage storage, pool house storage, dock storage",
        "outdoor_features": "Pool, spa, dock, outdoor kitchen, fire pit",
        "recent_improvements": "New roof (2023), updated HVAC (2022), pool resurfaced (2024)",
        "warranty_information": "10-year structural warranty, 2-year HVAC warranty",
        "disclosure_items": "Property is in flood zone X, no flood damage history",
        "additional_terms": "Seller to provide 1-year home warranty",
        "contingencies": "Subject to buyer's inspection and financing approval",
        "special_conditions": "Dock and boat lift to remain in working condition",
        "possession_date": closing_date,
        "key_information": "Electronic locks, codes to be provided at closing",
        "alarm_codes": "Security codes to be changed after closing",
        "utility_transfers": "All utilities to be transferred to buyer's name at closing",
        "final_walkthrough": "24 hours prior to closing",
        "document_delivery": "All documents via email and overnight delivery",
        "communication_preferences": "Email preferred, phone for urgent matters",
        
        # BUYER INITIALS (Initials 1-18 for buyer)
        "buyer_initial_1": "MR",
        "buyer_initial_2": "MR", 
        "buyer_initial_3": "MR",
        "buyer_initial_4": "MR",
        "buyer_initial_5": "MR",
        "buyer_initial_6": "MR",
        "buyer_initial_7": "MR",
        "buyer_initial_8": "MR",
        "buyer_initial_9": "MR",
        "buyer_initial_10": "MR",
        "buyer_initial_11": "MR",
        "buyer_initial_12": "MR",
        "buyer_initial_13": "MR",
        "buyer_initial_14": "MR",
        "buyer_initial_15": "MR",
        "buyer_initial_16": "MR",
        "buyer_initial_17": "MR",
        "buyer_initial_18": "MR",
        
        # SELLER INITIALS (Initials 19-36 for seller)
        "seller_initial_1": "SJ",
        "seller_initial_2": "SJ",
        "seller_initial_3": "SJ",
        "seller_initial_4": "SJ",
        "seller_initial_5": "SJ",
        "seller_initial_6": "SJ",
        "seller_initial_7": "SJ",
        "seller_initial_8": "SJ",
        "seller_initial_9": "SJ",
        "seller_initial_10": "SJ",
        "seller_initial_11": "SJ",
        "seller_initial_12": "SJ",
        "seller_initial_13": "SJ",
        "seller_initial_14": "SJ",
        "seller_initial_15": "SJ",
        "seller_initial_16": "SJ",
        "seller_initial_17": "SJ",
        "seller_initial_18": "SJ",
        
        # SIGNATURES AND FINAL DATES
        "buyer_signature_1": "Michael A. Rodriguez",
        "buyer_signature_2": "Maria C. Rodriguez", 
        "seller_signature_1": "Sarah E. Johnson",
        "seller_signature_2": "",  # Single seller
        "signature_date_buyer_1": contract_date,
        "signature_date_buyer_2": contract_date,
        "signature_date_seller_1": contract_date,
        "signature_date_seller_2": "",
        "notary_signature": "Jennifer L. Martinez, Notary Public",
        "notary_date": contract_date,
        "witness_1": "David K. Thompson",
        "witness_2": "Lisa M. Anderson",
        "witness_date_1": contract_date,
        "witness_date_2": contract_date
    }


def test_comprehensive_pdf_filling():
    """Test filling all 188 fields in the PDF"""

    print("🏠 COMPREHENSIVE PDF FILLING TEST - ALL 188 FIELDS")
    print("=" * 60)

    # Step 0: Create necessary files
    print("0️⃣ Setting up required files...")

    # Create PDF fields file
    if not create_pdf_fields_file():
        print("❌ Failed to create PDF fields file. Continuing anyway...")

    # Create comprehensive field mapping
    field_mapping = create_comprehensive_field_mapping()

    # Upload the mapping to the backend
    try:
        response = requests.post(
            f"{BACKEND_URL}/update-mapping",
            json=field_mapping,
            headers={'Content-Type': 'application/json'}
        )
        if response.status_code == 200:
            print("✅ Field mapping uploaded to backend")
        else:
            print(f"⚠️ Failed to upload mapping: {response.status_code}")
    except Exception as e:
        print(f"⚠️ Error uploading mapping: {e}")

    # Create comprehensive form data
    form_data = create_comprehensive_form_data()

    print(f"\n📋 Created form data with {len(form_data)} fields")
    print(f"💰 Purchase Price: ${form_data['purchase_price']:,}")
    print(f"🏦 Loan Amount: ${form_data['loan_amount']:,}")
    print(f"👥 Buyer: {form_data['buyer_name']}")
    print(f"👤 Seller: {form_data['seller_name']}")
    print(f"🏡 Property: {form_data['property_address']}")

    # Count checkbox items
    checkbox_count = 0
    for key, value in form_data.items():
        if isinstance(value, dict):
            checkbox_count += len(value)

    print(f"☑️ Checkbox options: {checkbox_count}")
    print(f"✍️ Buyer initials: 18 locations")
    print(f"✍️ Seller initials: 18 locations")
    
    # Step 1: Validate the form data
    print("\n1️⃣ Validating comprehensive form data...")
    try:
        response = requests.post(
            f"{BACKEND_URL}/validate-form",
            json=form_data,
            headers={'Content-Type': 'application/json'}
        )

        if response.status_code == 200:
            validation_result = response.json()
            print(f"✅ Validation Status: {validation_result['status']}")
            print(f"✅ Valid: {validation_result['valid']}")
            print(f"✅ Processed Fields: {validation_result['processed_fields']}")

            if validation_result.get('warnings'):
                print(f"⚠️ Warnings: {len(validation_result['warnings'])}")
                for i, warning in enumerate(validation_result['warnings'][:5]):
                    print(f"   {i+1}. {warning}")
                if len(validation_result['warnings']) > 5:
                    print(f"   ... and {len(validation_result['warnings']) - 5} more warnings")

            if validation_result.get('errors'):
                print(f"❌ Errors: {len(validation_result['errors'])}")
                for error in validation_result['errors']:
                    print(f"   - {error}")
        else:
            print(f"❌ Validation failed: {response.text}")

    except Exception as e:
        print(f"❌ Validation error: {e}")
    
    # Step 2: Generate the comprehensive filled PDF
    print("\n2️⃣ Generating comprehensive filled PDF...")
    try:
        response = requests.post(
            f"{BACKEND_URL}/fill-pdf",
            json=form_data,
            headers={'Content-Type': 'application/json'}
        )

        if response.status_code == 200:
            # Save the PDF file
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"COMPREHENSIVE_filled_contract_{timestamp}.pdf"

            with open(filename, 'wb') as f:
                f.write(response.content)

            print(f"🎉 SUCCESS! Comprehensive PDF generated: {filename}")
            print(f"📄 File size: {len(response.content):,} bytes")
            print(f"💾 Saved to: {filename}")

            # Show summary of what was filled
            print(f"\n📊 FILLING SUMMARY:")
            print(f"   💰 Purchase Price: ${form_data['purchase_price']:,}")
            print(f"   🏦 Loan Amount: ${form_data['loan_amount']:,}")
            print(f"   💵 Down Payment: ${form_data['down_payment']:,}")
            print(f"   📅 Contract Date: {form_data['contract_date']}")
            print(f"   📅 Closing Date: {form_data['closing_date']}")
            print(f"   ✅ Financing: Conventional loan selected")
            print(f"   ✅ Inspections: {sum(form_data['inspections'].values())} inspections selected")
            print(f"   ✅ Appliances: {sum(form_data['appliances'].values())} appliances included")
            print(f"   ✍️ Buyer Initials: {form_data['buyer_initial_1']} (18 locations)")
            print(f"   ✍️ Seller Initials: {form_data['seller_initial_1']} (18 locations)")
            print(f"   📝 Signatures: Buyer and Seller signatures included")
            
        else:
            print(f"❌ PDF generation failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Status: {error_data.get('status', 'unknown')}")
                print(f"   Message: {error_data.get('message', 'No message')}")
                print(f"   Fields processed: {error_data.get('fields_processed', 0)}")
                print(f"   Fields failed: {error_data.get('fields_failed', 0)}")
                
                if error_data.get('errors'):
                    print("   Errors:")
                    for error in error_data['errors'][:3]:
                        print(f"     - {error}")
                
                if error_data.get('warnings'):
                    print("   Warnings:")
                    for warning in error_data['warnings'][:3]:
                        print(f"     - {warning}")
                        
            except json.JSONDecodeError:
                print(f"   Raw response: {response.text}")
            
    except Exception as e:
        print(f"❌ PDF generation error: {e}")
    
    print("\n" + "=" * 60)
    print("🏁 COMPREHENSIVE TEST COMPLETED!")
    print("Check the generated PDF to see all 188 fields filled with realistic data.")


if __name__ == "__main__":
    print("🚀 Starting comprehensive PDF filling test...")
    print("Make sure the Flask server is running on http://localhost:5000")
    print()
    
    test_comprehensive_pdf_filling()
