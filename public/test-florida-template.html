<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FloridaTemplate.pdf Mapping Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .field-mapping {
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px;
            margin: 2px 0;
            border-radius: 3px;
        }
        .summary {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #results {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>FloridaTemplate.pdf Field Mapping Test</h1>
        <p>This page tests the mapping between form data and FloridaTemplate.pdf field names.</p>
        
        <button onclick="runBasicTest()">Run Basic Mapping Test</button>
        <button onclick="runValidationTest()">Run Validation Test</button>
        <button onclick="runEdgeCaseTest()">Run Edge Case Test</button>
        <button onclick="clearResults()">Clear Results</button>
        
        <div id="results"></div>
    </div>

    <script>
        // Mock FormData structure for testing
        const createTestFormData = () => {
            return {
                propertyInfo: {
                    sellerName: 'SARAH JOHNSON',
                    buyerName: 'MICHAEL RODRIGUEZ',
                    streetAddress: '123 Ocean Drive, Miami Beach, FL 33139',
                    county: 'Miami-Dade',
                    propertyTaxId: '12-3456-789-0123',
                    legalDescription1: 'Lot 15, Block 3, SUNSET ESTATES SUBDIVISION',
                    legalDescription2: 'According to the plat thereof recorded in Plat Book 45, Page 67',
                    personalPropertyIncluded: 'All kitchen appliances including refrigerator, washer, dryer, microwave, dishwasher, and security system',
                    additionalPersonalProperty: 'Window treatments and ceiling fans',
                    itemsExcluded: 'Outdoor furniture and artwork'
                },
                financialInfo: {
                    purchasePrice: 750000,
                    initialDeposit: 75000,
                    balanceToClose: 675000,
                    closingDate: '2025-08-15'
                },
                paymentMethod: {
                    paymentType: 'financing',
                    loanType: 'conventional',
                    financingAmount: 600000,
                    interestRateType: 'fixed'
                },
                partyDetails: {
                    contractDate: '2025-07-15',
                    sellerInitials: 'SJ',
                    buyerInitials: 'MR'
                },
                escrowInfo: {
                    escrowAgentName: 'Sunshine Title Company',
                    escrowAgentAddress: '456 Title Street, Miami, FL 33101'
                },
                titleClosingLogic: {
                    closingOption: 'seller-designates'
                },
                additionalTerms: [
                    'Property sold AS-IS with right to inspect',
                    'Seller to provide termite inspection',
                    'Buyer to verify all permits and certificates'
                ]
            };
        };

        // FloridaTemplate mapping function (simplified version)
        const mapFormDataToFloridaTemplate = (formData) => {
            const mapping = {};
            
            // Basic field mappings
            mapping["'(Text_1)'"] = formData.propertyInfo.sellerName || '';
            mapping["'(Text_2)'"] = formData.propertyInfo.buyerName || '';
            mapping["'(Text_3)'"] = formData.propertyInfo.streetAddress || '';
            mapping["'(Text_4)'"] = formData.propertyInfo.county || '';
            mapping["'(Text_5)'"] = formData.propertyInfo.propertyTaxId || '';
            mapping["'(Text_6)'"] = formData.propertyInfo.legalDescription1 || '';
            mapping["'(Text_7)'"] = formData.propertyInfo.legalDescription2 || '';
            mapping["'(Text_9)'"] = formData.propertyInfo.personalPropertyIncluded || '';
            mapping["'(Text_10)'"] = formData.propertyInfo.additionalPersonalProperty || '';
            mapping["'(Text_11)'"] = formData.propertyInfo.itemsExcluded || '';
            
            // Financial fields
            mapping["'(Number_1)'"] = formData.financialInfo.purchasePrice?.toString() || '';
            mapping["'(Number_2)'"] = formData.financialInfo.initialDeposit?.toString() || '';
            mapping["'(Number_3)'"] = formData.financialInfo.balanceToClose?.toString() || '';
            
            // Payment method checkboxes
            mapping["'(Checkbox_8)'"] = formData.paymentMethod.paymentType === 'cash';
            mapping["'(Checkbox_9)'"] = formData.paymentMethod.loanType === 'conventional';
            mapping["'(Checkbox_10)'"] = formData.paymentMethod.loanType === 'fha';
            mapping["'(Checkbox_11)'"] = formData.paymentMethod.loanType === 'va';
            
            // Dates
            mapping["'(Date_1)'"] = formData.partyDetails.contractDate || '';
            
            // Initials - populate multiple fields
            const sellerInitials = formData.partyDetails.sellerInitials || '';
            const buyerInitials = formData.partyDetails.buyerInitials || '';
            for (let i = 1; i <= 36; i++) {
                const isSellerField = i % 2 === 1;
                mapping[`'(Initials_${i})'`] = isSellerField ? sellerInitials : buyerInitials;
            }
            
            return mapping;
        };

        const validateRequiredFields = (formData) => {
            const missing = [];
            const requiredFields = [
                { field: 'sellerName', path: 'propertyInfo.sellerName' },
                { field: 'buyerName', path: 'propertyInfo.buyerName' },
                { field: 'streetAddress', path: 'propertyInfo.streetAddress' },
                { field: 'county', path: 'propertyInfo.county' },
                { field: 'purchasePrice', path: 'financialInfo.purchasePrice' },
                { field: 'contractDate', path: 'partyDetails.contractDate' }
            ];
            
            requiredFields.forEach(({ field, path }) => {
                const value = path.split('.').reduce((obj, key) => obj?.[key], formData);
                if (!value || (typeof value === 'string' && !value.trim())) {
                    missing.push(field);
                }
            });
            
            return missing;
        };

        const displayResults = (html) => {
            document.getElementById('results').innerHTML += html;
        };

        const clearResults = () => {
            document.getElementById('results').innerHTML = '';
        };

        const runBasicTest = () => {
            displayResults('<div class="test-section"><h3>Basic Mapping Test</h3>');
            
            const testData = createTestFormData();
            const mappedFields = mapFormDataToFloridaTemplate(testData);
            
            displayResults(`<p class="success">✓ Mapped ${Object.keys(mappedFields).length} fields</p>`);
            
            // Test key fields
            const keyTests = [
                { field: "'(Text_1)'", expected: 'SARAH JOHNSON', description: 'Seller Name' },
                { field: "'(Text_2)'", expected: 'MICHAEL RODRIGUEZ', description: 'Buyer Name' },
                { field: "'(Text_3)'", expected: '123 Ocean Drive, Miami Beach, FL 33139', description: 'Property Address' },
                { field: "'(Number_1)'", expected: '750000', description: 'Purchase Price' },
                { field: "'(Checkbox_9)'", expected: true, description: 'Conventional Loan' },
                { field: "'(Date_1)'", expected: '2025-07-15', description: 'Contract Date' }
            ];
            
            let passed = 0;
            keyTests.forEach(test => {
                const actual = mappedFields[test.field];
                const success = actual === test.expected;
                if (success) {
                    displayResults(`<div class="field-mapping success">✓ ${test.description}: ${test.field} = "${actual}"</div>`);
                    passed++;
                } else {
                    displayResults(`<div class="field-mapping error">✗ ${test.description}: ${test.field} = "${actual}" (expected: "${test.expected}")</div>`);
                }
            });
            
            displayResults(`<p class="summary">Passed ${passed}/${keyTests.length} key field tests</p></div>`);
        };

        const runValidationTest = () => {
            displayResults('<div class="test-section"><h3>Validation Test</h3>');
            
            const testData = createTestFormData();
            const missingFields = validateRequiredFields(testData);
            
            if (missingFields.length === 0) {
                displayResults('<p class="success">✓ All required fields are present</p>');
            } else {
                displayResults(`<p class="error">✗ Missing required fields: ${missingFields.join(', ')}</p>`);
            }
            
            displayResults('</div>');
        };

        const runEdgeCaseTest = () => {
            displayResults('<div class="test-section"><h3>Edge Case Test</h3>');
            
            // Test with minimal data
            const minimalData = {
                propertyInfo: {
                    sellerName: 'John Doe',
                    buyerName: 'Jane Smith',
                    streetAddress: '123 Main St, City, FL 12345',
                    county: 'Orange',
                    propertyTaxId: '',
                    legalDescription1: '',
                    legalDescription2: '',
                    personalPropertyIncluded: '',
                    additionalPersonalProperty: '',
                    itemsExcluded: ''
                },
                financialInfo: {
                    purchasePrice: 100000,
                    initialDeposit: 5000,
                    balanceToClose: 95000,
                    closingDate: ''
                },
                paymentMethod: {
                    paymentType: 'cash',
                    loanType: undefined,
                    financingAmount: undefined,
                    interestRateType: undefined
                },
                partyDetails: {
                    contractDate: '2025-07-15',
                    sellerInitials: 'JD',
                    buyerInitials: 'JS'
                },
                escrowInfo: {
                    escrowAgentName: '',
                    escrowAgentAddress: ''
                },
                titleClosingLogic: {
                    closingOption: 'buyer-designates'
                },
                additionalTerms: []
            };
            
            const mappedMinimal = mapFormDataToFloridaTemplate(minimalData);
            const missingFieldsMinimal = validateRequiredFields(minimalData);
            
            displayResults(`<p class="success">✓ Mapped ${Object.keys(mappedMinimal).length} fields with minimal data</p>`);
            displayResults(`<p>Missing required fields: ${missingFieldsMinimal.length}</p>`);
            
            // Test cash payment checkbox
            const cashCheckbox = mappedMinimal["'(Checkbox_8)'"];
            displayResults(`<p class="success">✓ Cash payment checkbox: ${cashCheckbox} (should be true for cash payment)</p>`);
            
            displayResults('</div>');
        };

        // Run basic test on page load
        window.onload = () => {
            displayResults('<div class="summary"><h3>FloridaTemplate.pdf Mapping System Ready</h3><p>Click the buttons above to run tests.</p></div>');
        };
    </script>
</body>
</html>
