# Florida As-Is Purchase Agreement PDF Field Mapping Report

**Document Version:** 2.0  
**Last Updated:** January 2025  
**Total PDF Fields:** 180+  
**Form Type:** Florida "As-Is" Residential Contract For Sale And Purchase

---

## CRITICAL FIELD MAPPINGS

### 1. PARTIES SECTION
| PDF Field Name | Expected Format | Form Data Source | Notes |
|----------------|-----------------|------------------|-------|
| `PARTIES` | `"JOHN DOE ("Seller"), and JANE SMITH ("Buyer")"` | `${sellerName} ("Seller"), and ${buyerName} ("Buyer")` | **CRITICAL**: Exact format required |

### 2. PROPERTY INFORMATION
| PDF Field Name | Expected Value | Form Data Source | Notes |
|----------------|----------------|------------------|-------|
| `a Street address city zip` | `"123 Main Street, Miami, FL 33101"` | `propertyInfo.streetAddress` | **CRITICAL**: Property address only |
| `County Florida Property Tax ID` | `"Miami-Dade, Florida"` | `${propertyInfo.county}, Florida` | **CRITICAL**: County only, no tax ID |
| `b Located in` | `"12-3456789"` | `propertyInfo.propertyTaxId` | **CRITICAL**: Tax ID only, separate field |
| `c Real Property The legal description is 1` | Legal description line 1 | `propertyInfo.legalDescription1` | Multi-line legal text |
| `c Real Property The legal description is 2` | Legal description line 2 | `propertyInfo.legalDescription2` | Continuation of legal text |

### 3. FINANCIAL AMOUNTS (Currency Fields)
| PDF Field Name | Expected Format | Form Data Source | Notes |
|----------------|-----------------|------------------|-------|
| `Text79` | `"$500,000"` | `formatCurrency(financialInfo.purchasePrice)` | Purchase Price |
| `Text80` | `"$25,000"` | `formatCurrency(financialInfo.initialDeposit)` | Initial Deposit |
| `Text81` | `""` | Empty | Additional Deposit |
| `Text82` | `"$300,000"` | `formatCurrency(paymentMethod.financingAmount)` | Financing Amount |
| `Text83` | `""` | Empty | Other Amount |
| `Text84` | `"$170,000"` | `formatCurrency(financialInfo.balanceToClose)` | Balance to Close |

### 4. PERSONAL PROPERTY
| PDF Field Name | Expected Value | Form Data Source | Notes |
|----------------|----------------|------------------|-------|
| `and other access devices and storm shutterspanels Personal Property` | Personal property list | `propertyInfo.personalPropertyIncluded` | Included items |
| `Other Personal Property items included in this purchase are` | Additional items | `propertyInfo.additionalPersonalProperty` | Extra inclusions |
| `e The following items are excluded from the purchase` | Excluded items | `propertyInfo.itemsExcluded` | Items not included |

### 5. ESCROW AGENT INFORMATION
| PDF Field Name | Expected Value | Form Data Source | Notes |
|----------------|----------------|------------------|-------|
| `Escrow Agent Information Name` | `"ABC Title Company"` | `escrowInfo.escrowAgentName` | Title company name |
| `Address` | Multi-line address | `escrowInfo.escrowAgentAddress` | Title company address |
| `Email` | Email address | Empty for now | Optional field |
| `Fax` | Fax number | Empty for now | Optional field |

### 6. DATES
| PDF Field Name | Expected Format | Form Data Source | Notes |
|----------------|-----------------|------------------|-------|
| `Closing Date at the time established by the Closing Agent` | `"July 1, 2024"` | `formatDate(financialInfo.closingDate)` | Closing date |
| `Date` | `"June 1, 2024"` | `formatDate(partyDetails.contractDate)` | Contract date |
| `Date_2` | `"June 1, 2024"` | `formatDate(partyDetails.contractDate)` | Contract date (repeat) |
| `Date_3` | `"June 1, 2024"` | `formatDate(partyDetails.contractDate)` | Contract date (repeat) |
| `Date_4` | `"June 1, 2024"` | `formatDate(partyDetails.contractDate)` | Contract date (repeat) |

### 7. BUYER INITIALS (All Pages)
| PDF Field Name | Expected Value | Form Data Source | Notes |
|----------------|----------------|------------------|-------|
| `Buyers Initials` | `"JS"` | `partyDetails.buyerInitials` | Primary buyer initials |
| `Buyers Initials_2` | `"JS"` | `partyDetails.buyerInitials` | Page 2 buyer initials |
| `undefined_3` through `undefined_15` | `"JS"` | `partyDetails.buyerInitials` | Additional buyer initial fields |

### 8. SELLER INITIALS (All Pages)
| PDF Field Name | Expected Value | Form Data Source | Notes |
|----------------|----------------|------------------|-------|
| `Sellers Initials` | `"JD"` | `partyDetails.sellerInitials` | Primary seller initials |
| `Sellers Initials_2` through `Sellers Initials_11` | `"JD"` | `partyDetails.sellerInitials` | Additional seller initial fields |

### 9. PAYMENT METHOD CHECKBOXES
| PDF Field Name | Checked When | Form Data Source | Notes |
|----------------|--------------|------------------|-------|
| `a Buyer will pay cash for the purchase of the Property at Closing There is no financing contingency to Buyers` | Cash purchase | `paymentMethod.paymentType === 'cash'` | Cash option |
| `b This Contract is contingent upon Buyer obtaining approval of a` | Financing needed | `paymentMethod.paymentType === 'financing'` | Financing option |

### 10. LOAN TYPE CHECKBOXES
| PDF Field Name | Checked When | Form Data Source | Notes |
|----------------|--------------|------------------|-------|
| `conventional` | Conventional loan | `paymentMethod.loanType === 'Conventional'` | Conventional loan |
| `FHA` | FHA loan | `paymentMethod.loanType === 'FHA'` | FHA loan |
| `VA or` | VA loan | `paymentMethod.loanType === 'VA'` | VA loan |
| `other` | Other loan type | `paymentMethod.loanType === 'Other'` | Other loan |

### 11. INTEREST RATE TYPE CHECKBOXES
| PDF Field Name | Checked When | Form Data Source | Notes |
|----------------|--------------|------------------|-------|
| `fixed` | Fixed rate | `paymentMethod.interestRateType === 'Fixed'` | Fixed rate loan |
| `adjustable` | Adjustable rate | `paymentMethod.interestRateType === 'Adjustable'` | Adjustable rate loan |

### 12. CLOSING COST RESPONSIBILITY CHECKBOXES
| PDF Field Name | Checked When | Form Data Source | Notes |
|----------------|--------------|------------------|-------|
| `i Seller shall designate Closing Agent and pay for Owners Policy and Charges and Buyer shall pay the` | Seller designates | `titleClosingLogic.closingOption === 'seller-designates'` | Seller responsibility |
| `ii Buyer shall designate Closing Agent and pay for Owners Policy and Charges and charges for closing` | Buyer designates | `titleClosingLogic.closingOption === 'buyer-designates'` | Buyer responsibility |

### 13. DEPOSIT OPTIONS CHECKBOXES
| PDF Field Name | Checked When | Form Data Source | Notes |
|----------------|--------------|------------------|-------|
| `accompanies offer or ii` | Deposit with offer | `financialInfo.initialDeposit > 0` | Deposit accompanies |
| `blank then 3 days after Effective Date IF NEITHER BOX IS CHECKED THEN` | No immediate deposit | `financialInfo.initialDeposit === 0` | 3-day deposit |

### 14. ADDITIONAL TERMS (16 Fields)
| PDF Field Name | Expected Value | Form Data Source | Notes |
|----------------|----------------|------------------|-------|
| `20 ADDITIONAL TERMS 1` | First additional term | `additionalTerms[0]` | Term 1 |
| `20 ADDITIONAL TERMS 2` | Second additional term | `additionalTerms[1]` | Term 2 |
| `...` | ... | `...` | ... |
| `20 ADDITIONAL TERMS 16` | Sixteenth additional term | `additionalTerms[15]` | Term 16 |

### 15. ADDRESS FOR NOTICES
| PDF Field Name | Expected Value | Form Data Source | Notes |
|----------------|----------------|------------------|-------|
| `Buyers address for purposes of notice 1` | Buyer name | `propertyInfo.buyerName` | Buyer name line |
| `Buyers address for purposes of notice 2` | Property address | `propertyInfo.streetAddress` | Buyer address line |
| `Buyers address for purposes of notice 3` | Additional info | Empty | Optional line |
| `Sellers address for purposes of notice 1` | Seller name | `propertyInfo.sellerName` | Seller name line |
| `Sellers address for purposes of notice 2` | Property address | `propertyInfo.streetAddress` | Seller address line |
| `Sellers address for purposes of notice 3` | Additional info | Empty | Optional line |

### 16. CONTRACT RIDERS (CHECKBOXES)
| PDF Field Name | Checked When | Form Data Source | Notes |
|----------------|--------------|------------------|-------|
| `A Condominium Rider` | Condo property | Always false for now | Future feature |
| `B Homeowners Assn` | HOA property | Always false for now | Future feature |
| `C Seller Financing` | Seller financing | Always false for now | Future feature |
| `F Appraisal Contingency` | Appraisal needed | Always false for now | Future feature |
| `G Short Sale` | Short sale | Always false for now | Future feature |

### 17. REAL ESTATE PROFESSIONALS
| PDF Field Name | Expected Value | Form Data Source | Notes |
|----------------|----------------|------------------|-------|
| `Cooperating Sales Associate if any` | Buyer's agent | Empty for now | Optional |
| `Listing Sales Associate` | Listing agent | Empty for now | Optional |
| `Cooperating Broker if any` | Buyer's broker | Empty for now | Optional |
| `Listing Broker` | Listing broker | Empty for now | Optional |

### 18. UNUSED/EMPTY FIELDS
| PDF Field Name | Expected Value | Notes |
|----------------|----------------|-------|
| `Text85` through `Text96` | Empty | Reserved for future use |
| `Text103` | Empty | Reserved for future use |
| `undefined_2` | Empty | Reserved field |

---

## VALIDATION RULES

### Field Type Validation
- **Currency Fields (Text79-84)**: Must contain formatted currency (`$XXX,XXX`)
- **Address Fields**: Must not contain currency values or tax IDs
- **Tax ID Fields**: Must follow XX-XXXXXXX format
- **Date Fields**: Must be valid dates in readable format
- **Initials**: 1-4 uppercase letters only
- **Names**: No numbers allowed

### Critical Data Separation
1. **PARTIES field**: Contains both seller and buyer names in specific format
2. **County field**: Contains only county name + "Florida"
3. **Tax ID field**: Contains only the property tax ID number
4. **Address field**: Contains only the property street address

### Checkbox Logic
- Payment method checkboxes are mutually exclusive
- Loan type checkboxes are mutually exclusive
- Closing responsibility checkboxes are mutually exclusive
- Deposit timing checkboxes are mutually exclusive

---

## COMMON MAPPING ERRORS TO AVOID

1. **❌ WRONG**: Putting purchase price in address field
2. **❌ WRONG**: Combining county and tax ID in one field
3. **❌ WRONG**: Using individual names in PARTIES field instead of combined format
4. **❌ WRONG**: Not formatting currency values with $ and commas
5. **❌ WRONG**: Using raw date strings instead of formatted dates

## CORRECT MAPPING EXAMPLES

```javascript
// ✅ CORRECT PARTIES mapping
"PARTIES": "JOHN DOE (\"Seller\"), and JANE SMITH (\"Buyer\")"

// ✅ CORRECT address mapping
"a Street address city zip": "123 Main Street, Miami, FL 33101"

// ✅ CORRECT county mapping
"County Florida Property Tax ID": "Miami-Dade, Florida"

// ✅ CORRECT tax ID mapping
"b Located in": "12-3456789"

// ✅ CORRECT currency mapping
"Text79": "$500,000"
```

---

**End of Field Mapping Report**