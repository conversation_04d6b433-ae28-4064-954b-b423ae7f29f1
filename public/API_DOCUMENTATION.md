# PDF Form Filler API Documentation

A powerful REST API for filling PDF forms with dynamic data, featuring advanced checkbox handling and comprehensive field mapping.

## 🚀 **API Base URL**
```
https://pdf-form-filler-api.fly.dev
```

## 📋 **Table of Contents**
- [Quick Start](#quick-start)
- [Authentication](#authentication)
- [API Endpoints](#api-endpoints)
- [Request/Response Format](#requestresponse-format)
- [Checkbox Handling](#checkbox-handling)
- [Error Handling](#error-handling)
- [Frontend Integration Examples](#frontend-integration-examples)
- [Testing](#testing)

## 🏁 **Quick Start**

### Basic PDF Generation
```javascript
const response = await fetch('https://pdf-form-filler-api.fly.dev/fill-pdf', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    seller_name: "<PERSON>",
    buyer_name: "<PERSON>",
    purchase_price: 750000,
    financing: {
      cash: true,
      conventional: false,
      fha: true
    }
  })
});

if (response.ok) {
  const pdfBlob = await response.blob();
  // Handle PDF (download, display, etc.)
} else {
  const error = await response.json();
  console.error('Error:', error.message);
}
```

## 🔐 **Authentication**
Currently, no authentication is required. The API is open for testing purposes.

## 📡 **API Endpoints**

### 1. Health Check
**GET** `/health`

Check if the API is running.

**Response:**
```json
{
  "status": "healthy"
}
```

### 2. Form Validation
**POST** `/validate-form`

Validate form data without generating a PDF.

**Request Body:**
```json
{
  "seller_name": "John Smith",
  "financing": {
    "cash": true,
    "fha": false
  }
}
```

**Response:**
```json
{
  "status": "success",
  "valid": true,
  "errors": [],
  "warnings": [],
  "processed_fields": 3
}
```

### 3. PDF Generation
**POST** `/fill-pdf`

Generate a filled PDF form.

**Headers:**
- `Content-Type: application/json`
- `X-Debug-Checkboxes: true` (optional, for debugging)

**Request Body:** See [Request Format](#request-format)

**Response:** PDF file (binary data)

### 4. Debug Checkboxes
**GET** `/debug-checkboxes`

Get information about all checkbox fields in the PDF template.

**Response:**
```json
{
  "status": "success",
  "total_checkboxes": 73,
  "summary": {
    "mapped_checkboxes": 58,
    "unmapped_checkboxes": 15,
    "currently_checked": 0
  },
  "checkboxes": [...]
}
```

## 📝 **Request/Response Format**

### Request Format
The API accepts a JSON object with the following structure:

```json
{
  // Basic Information
  "seller_name": "John Michael Smith",
  "buyer_name": "Jane Elizabeth Doe",
  "purchase_price": 875000,
  "property_address": "1234 Maple Street, Beverly Hills, CA 90210",
  "closing_date": "2025-08-15",
  
  // Financing Information
  "financing": {
    "cash": false,
    "conventional": true,
    "fha": false,
    "va": false,
    "usda": false,
    "other": false
  },
  "loan_amount": 700000,
  "down_payment": 175000,
  "interest_rate": 6.75,
  "loan_term": 30,
  "lender_name": "First National Bank",
  
  // Inspections
  "inspections": {
    "as_is_condition": false,
    "buyer_inspection": true,
    "professional_inspection": true,
    "termite_inspection": true,
    "roof_inspection": true
  },
  "inspection_period": 10,
  "repair_limit": 5000,
  
  // Appliances
  "appliances": {
    "refrigerator": true,
    "washer": true,
    "dryer": true,
    "dishwasher": true,
    "microwave": false,
    "oven_range": true
  },
  
  // Agent Information
  "seller_agent": {
    "name": "Robert Johnson",
    "company": "Premium Realty Group",
    "license": "CA-DRE-1234567",
    "phone": "(*************",
    "email": "<EMAIL>"
  },
  "buyer_agent": {
    "name": "Sarah Williams",
    "company": "Elite Properties Inc",
    "license": "CA-DRE-7654321",
    "phone": "(*************",
    "email": "<EMAIL>"
  }
}
```

### Response Format

**Success Response:**
- **Content-Type:** `application/pdf`
- **Body:** PDF file binary data

**Error Response:**
```json
{
  "status": "error",
  "message": "Error description",
  "details": "Additional error details (optional)"
}
```

## ☑️ **Checkbox Handling**

The API features advanced checkbox handling that accepts multiple input formats:

### Supported Input Types

| Input Type | Example Values | Result |
|------------|----------------|---------|
| **Boolean** | `true`, `false` | Direct conversion |
| **String** | `"true"`, `"yes"`, `"on"`, `"checked"`, `"selected"` | Converted to `true` |
| **String** | `"false"`, `"no"`, `"off"`, `"unchecked"`, `""` | Converted to `false` |
| **Numeric** | `1`, `2`, `0.5`, `-1` | Non-zero = `true` |
| **Numeric** | `0` | Converted to `false` |

### Checkbox Groups

Checkboxes are organized in groups:

```json
{
  "financing": {
    "cash": true,           // ✅ Checked
    "conventional": false,  // ☐ Unchecked
    "fha": "yes",          // ✅ Checked (string converted)
    "va": 0                // ☐ Unchecked (numeric converted)
  },
  "inspections": {
    "as_is_condition": "on",      // ✅ Checked
    "buyer_inspection": "off",    // ☐ Unchecked
    "professional_inspection": 1  // ✅ Checked
  }
}
```

## ❌ **Error Handling**

### HTTP Status Codes
- `200` - Success
- `400` - Bad Request (invalid JSON, missing required fields)
- `500` - Internal Server Error

### Error Response Examples

**Invalid JSON:**
```json
{
  "status": "error",
  "message": "Invalid JSON data provided"
}
```

**PDF Generation Error:**
```json
{
  "status": "error",
  "message": "PDF generation failed",
  "details": "Template file not found"
}
```

## 💻 **Frontend Integration Examples**

### React Example
```jsx
import React, { useState } from 'react';

const PDFGenerator = () => {
  const [formData, setFormData] = useState({
    seller_name: '',
    buyer_name: '',
    purchase_price: 0,
    financing: {
      cash: false,
      conventional: true,
      fha: false
    }
  });
  const [loading, setLoading] = useState(false);

  const generatePDF = async () => {
    setLoading(true);
    try {
      const response = await fetch('https://pdf-form-filler-api.fly.dev/fill-pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Debug-Checkboxes': 'true'
        },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'contract.pdf';
        a.click();
        window.URL.revokeObjectURL(url);
      } else {
        const error = await response.json();
        alert(`Error: ${error.message}`);
      }
    } catch (error) {
      alert(`Network error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <input
        type="text"
        placeholder="Seller Name"
        value={formData.seller_name}
        onChange={(e) => setFormData({...formData, seller_name: e.target.value})}
      />
      <input
        type="text"
        placeholder="Buyer Name"
        value={formData.buyer_name}
        onChange={(e) => setFormData({...formData, buyer_name: e.target.value})}
      />
      <input
        type="number"
        placeholder="Purchase Price"
        value={formData.purchase_price}
        onChange={(e) => setFormData({...formData, purchase_price: parseInt(e.target.value)})}
      />
      
      <div>
        <label>
          <input
            type="checkbox"
            checked={formData.financing.cash}
            onChange={(e) => setFormData({
              ...formData,
              financing: {...formData.financing, cash: e.target.checked}
            })}
          />
          Cash Purchase
        </label>
      </div>
      
      <button onClick={generatePDF} disabled={loading}>
        {loading ? 'Generating...' : 'Generate PDF'}
      </button>
    </div>
  );
};

export default PDFGenerator;
```

### Vanilla JavaScript Example
```javascript
class PDFFormFiller {
  constructor(apiUrl = 'https://pdf-form-filler-api.fly.dev') {
    this.apiUrl = apiUrl;
  }

  async validateForm(formData) {
    const response = await fetch(`${this.apiUrl}/validate-form`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(formData)
    });
    return await response.json();
  }

  async generatePDF(formData, options = {}) {
    const headers = { 'Content-Type': 'application/json' };
    if (options.debug) {
      headers['X-Debug-Checkboxes'] = 'true';
    }

    const response = await fetch(`${this.apiUrl}/fill-pdf`, {
      method: 'POST',
      headers,
      body: JSON.stringify(formData)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message);
    }

    return await response.blob();
  }

  async downloadPDF(formData, filename = 'contract.pdf') {
    const blob = await this.generatePDF(formData);
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
  }
}

// Usage
const pdfFiller = new PDFFormFiller();

const formData = {
  seller_name: "John Smith",
  buyer_name: "Jane Doe",
  purchase_price: 500000,
  financing: { cash: true, conventional: false }
};

pdfFiller.downloadPDF(formData, 'my-contract.pdf');
```

### Vue.js Example
```vue
<template>
  <div>
    <form @submit.prevent="generatePDF">
      <input v-model="formData.seller_name" placeholder="Seller Name" required />
      <input v-model="formData.buyer_name" placeholder="Buyer Name" required />
      <input v-model.number="formData.purchase_price" type="number" placeholder="Purchase Price" required />

      <div>
        <label>
          <input v-model="formData.financing.cash" type="checkbox" />
          Cash Purchase
        </label>
        <label>
          <input v-model="formData.financing.conventional" type="checkbox" />
          Conventional Loan
        </label>
      </div>

      <button type="submit" :disabled="loading">
        {{ loading ? 'Generating...' : 'Generate PDF' }}
      </button>
    </form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      formData: {
        seller_name: '',
        buyer_name: '',
        purchase_price: 0,
        financing: {
          cash: false,
          conventional: true
        }
      }
    };
  },
  methods: {
    async generatePDF() {
      this.loading = true;
      try {
        const response = await fetch('https://pdf-form-filler-api.fly.dev/fill-pdf', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Debug-Checkboxes': 'true'
          },
          body: JSON.stringify(this.formData)
        });

        if (response.ok) {
          const blob = await response.blob();
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = 'contract.pdf';
          a.click();
          window.URL.revokeObjectURL(url);
        } else {
          const error = await response.json();
          alert(`Error: ${error.message}`);
        }
      } catch (error) {
        alert(`Network error: ${error.message}`);
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>
```

## 🧪 **Testing**

### Test the API
```bash
# Health check
curl https://pdf-form-filler-api.fly.dev/health

# Form validation
curl -X POST https://pdf-form-filler-api.fly.dev/validate-form \
  -H "Content-Type: application/json" \
  -d '{"seller_name": "Test", "financing": {"cash": true}}'

# PDF generation
curl -X POST https://pdf-form-filler-api.fly.dev/fill-pdf \
  -H "Content-Type: application/json" \
  -d '{"seller_name": "Test Seller", "buyer_name": "Test Buyer", "purchase_price": 500000}' \
  --output test.pdf

# Debug checkboxes
curl https://pdf-form-filler-api.fly.dev/debug-checkboxes
```

### Complete Test Example
```bash
curl -X POST https://pdf-form-filler-api.fly.dev/fill-pdf \
  -H "Content-Type: application/json" \
  -H "X-Debug-Checkboxes: true" \
  -d '{
    "seller_name": "Michael Thompson",
    "buyer_name": "Lisa Anderson",
    "purchase_price": 650000,
    "property_address": "456 Oak Avenue, Sacramento, CA 95814",
    "closing_date": "2025-08-01",
    "financing": {
      "cash": false,
      "conventional": true,
      "fha": false,
      "va": false
    },
    "loan_amount": 520000,
    "down_payment": 130000,
    "inspections": {
      "as_is_condition": false,
      "buyer_inspection": true,
      "professional_inspection": true,
      "termite_inspection": true
    },
    "appliances": {
      "refrigerator": true,
      "washer": false,
      "dryer": true,
      "dishwasher": true,
      "microwave": true,
      "oven_range": false
    }
  }' \
  --output complete_test.pdf
```

### Debug Information
Add the `X-Debug-Checkboxes: true` header to get detailed logging for checkbox processing.

## 🔧 **Advanced Features**

### Field Mapping
The API automatically maps your JSON keys to PDF field names. Common mappings include:

| JSON Key | PDF Field | Type |
|----------|-----------|------|
| `seller_name` | Text field | String |
| `purchase_price` | Text field | Number |
| `financing.cash` | Checkbox_8 | Boolean |
| `financing.conventional` | Checkbox_9 | Boolean |
| `inspections.as_is_condition` | Checkbox_1 | Boolean |

### Response Headers
Successful PDF generation includes these headers:
- `Content-Type: application/pdf`
- `Content-Disposition: attachment; filename="filled_form.pdf"`

### Rate Limiting
Currently no rate limiting is implemented. For production use, consider implementing rate limiting on your frontend.

## 📞 **Support & Troubleshooting**

### Common Issues

**1. PDF Not Downloading**
- Check that the response status is 200
- Ensure you're handling the blob correctly
- Verify CORS headers if calling from a browser

**2. Checkboxes Not Appearing Checked**
- Use the `/debug-checkboxes` endpoint to see available fields
- Enable debug mode with `X-Debug-Checkboxes: true`
- Check that your boolean values are being sent correctly

**3. Fields Not Filling**
- Use `/validate-form` to check your data structure
- Verify field names match the expected format
- Check the API logs for field mapping issues

### Debug Endpoints
- `/debug-checkboxes` - Get checkbox field information
- `/validate-form` - Test data before PDF generation
- Add `X-Debug-Checkboxes: true` header for detailed logging

## 🔄 **API Versioning**
Current version: `v1` (no versioning prefix required)

## 🚀 **Performance**
- Average response time: < 2 seconds
- Maximum file size: ~3MB per PDF
- Concurrent requests: Supported
- Auto-scaling: Enabled on Fly.io

---

**API Status:** ✅ Production Ready
**Last Updated:** July 15, 2025
**Deployment:** https://pdf-form-filler-api.fly.dev
**Health Check:** https://pdf-form-filler-api.fly.dev/health
