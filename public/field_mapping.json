{"fields": {"seller_name": "'(Text_1)'", "buyer_name": "'(Text_2)'", "property_address": "'(Text_3)'", "county": "'(Text_4)'", "tax_id": "'(Text_5)'", "legal_description": "'(Text_6)'", "additional_property_details": "'(Text_7)'", "personal_property_included": "'(Text_8)'", "personal_property_excluded": "'(Text_9)'", "fixtures_included": "'(Text_10)'", "other_items": "'(Text_11)'", "seller_phone": "'(US_Phone_Number_1)'", "seller_email": "'(Email_1)'", "buyer_phone": "'(US_Phone_Number_2)'", "purchase_price": "'(Number_1)'", "initial_deposit": "'(Number_2)'", "additional_deposit": "'(Number_3)'", "loan_amount": "'(Number_4)'", "down_payment": "'(Number_5)'", "closing_costs": "'(Number_6)'", "inspection_fee": "'(Number_7)'", "appraisal_fee": "'(Number_8)'", "survey_cost": "'(Number_9)'", "contract_date": "'(Date_1)'", "closing_date": "'(Date_2)'", "inspection_deadline": "'(Date_3)'", "financing_deadline": "'(Date_4)'", "appraisal_deadline": "'(Date_5)'", "seller_attorney": "'(Text_12)'", "buyer_attorney": "'(Text_13)'", "title_company": "'(Text_14)'", "real_estate_agent_seller": "'(Text_15)'", "real_estate_agent_buyer": "'(Text_16)'", "lender_name": "'(Text_17)'", "loan_officer": "'(Text_18)'", "appraiser": "'(Text_19)'", "inspector": "'(Text_20)'", "surveyor": "'(Text_21)'", "insurance_agent": "'(Text_22)'", "hoa_management": "'(Text_23)'", "utilities_electric": "'(Text_24)'", "utilities_gas": "'(Text_25)'", "utilities_water": "'(Text_26)'", "utilities_cable": "'(Text_27)'", "utilities_internet": "'(Text_28)'", "utilities_trash": "'(Text_29)'", "utilities_security": "'(Text_30)'", "maintenance_pool": "'(Text_31)'", "maintenance_landscape": "'(Text_32)'", "maintenance_hvac": "'(Text_33)'", "maintenance_dock": "'(Text_34)'", "special_assessments": "'(Text_35)'", "hoa_fees": "'(Text_36)'", "property_taxes": "'(Text_37)'", "insurance_cost": "'(Text_38)'", "utility_costs": "'(Text_39)'", "maintenance_costs": "'(Text_40)'", "rental_restrictions": "'(Text_41)'", "pet_restrictions": "'(Text_42)'", "parking_spaces": "'(Text_43)'", "storage_areas": "'(Text_44)'", "outdoor_features": "'(Text_45)'", "recent_improvements": "'(Text_46)'", "warranty_information": "'(Text_47)'", "disclosure_items": "'(Text_48)'", "additional_terms": "'(Text_49)'", "contingencies": "'(Text_50)'", "special_conditions": "'(Text_51)'", "possession_date": "'(Text_52)'", "key_information": "'(Text_53)'", "alarm_codes": "'(Text_54)'", "utility_transfers": "'(Text_55)'", "final_walkthrough": "'(Text_56)'", "document_delivery": "'(Text_57)'", "communication_preferences": "'(Text_58)'", "buyer_initial_1": "'(Initials_1)'", "buyer_initial_2": "'(Initials_2)'", "buyer_initial_3": "'(Initials_3)'", "buyer_initial_4": "'(Initials_4)'", "buyer_initial_5": "'(Initials_5)'", "buyer_initial_6": "'(Initials_6)'", "buyer_initial_7": "'(Initials_7)'", "buyer_initial_8": "'(Initials_8)'", "buyer_initial_9": "'(Initials_9)'", "buyer_initial_10": "'(Initials_10)'", "buyer_initial_11": "'(Initials_11)'", "buyer_initial_12": "'(Initials_12)'", "buyer_initial_13": "'(Initials_13)'", "buyer_initial_14": "'(Initials_14)'", "buyer_initial_15": "'(Initials_15)'", "buyer_initial_16": "'(Initials_16)'", "buyer_initial_17": "'(Initials_17)'", "buyer_initial_18": "'(Initials_18)'", "seller_initial_1": "'(Initials_19)'", "seller_initial_2": "'(Initials_20)'", "seller_initial_3": "'(Initials_21)'", "seller_initial_4": "'(Initials_22)'", "seller_initial_5": "'(Initials_23)'", "seller_initial_6": "'(Initials_24)'", "seller_initial_7": "'(Initials_25)'", "seller_initial_8": "'(Initials_26)'", "seller_initial_9": "'(Initials_27)'", "seller_initial_10": "'(Initials_28)'", "seller_initial_11": "'(Initials_29)'", "seller_initial_12": "'(Initials_30)'", "seller_initial_13": "'(Initials_31)'", "seller_initial_14": "'(Initials_32)'", "seller_initial_15": "'(Initials_33)'", "seller_initial_16": "'(Initials_34)'", "seller_initial_17": "'(Initials_35)'", "seller_initial_18": "'(Initials_36)'", "buyer_signature_1": "'(Signature_1)'", "buyer_signature_2": "'(Signature_2)'", "seller_signature_1": "'(Signature_3)'", "seller_signature_2": "'(Signature_4)'"}, "checkbox_groups": {"financing": {"cash": "'(Checkbox_8)'", "conventional": "'(Checkbox_9)'", "fha": "'(Checkbox_10)'", "va": "'(Checkbox_11)'", "usda": "'(Checkbox_12)'", "other": "'(Checkbox_13)'"}, "inspections": {"as_is_condition": "'(Checkbox_1)'", "seller_repairs": "'(Checkbox_2)'", "buyer_inspection": "'(Checkbox_3)'", "professional_inspection": "'(Checkbox_4)'", "termite_inspection": "'(Checkbox_5)'", "roof_inspection": "'(Checkbox_6)'", "hvac_inspection": "'(Checkbox_7)'", "electrical_inspection": "'(Checkbox_14)'", "plumbing_inspection": "'(Checkbox_15)'", "pool_inspection": "'(Checkbox_16)'", "dock_inspection": "'(Checkbox_17)'", "environmental_inspection": "'(Checkbox_18)'", "lead_paint_inspection": "'(Checkbox_19)'", "radon_inspection": "'(Checkbox_20)'", "mold_inspection": "'(Checkbox_21)'", "structural_inspection": "'(Checkbox_22)'", "foundation_inspection": "'(Checkbox_23)'", "septic_inspection": "'(Checkbox_24)'", "well_inspection": "'(Checkbox_25)'", "survey_required": "'(Checkbox_26)'", "appraisal_required": "'(Checkbox_27)'"}, "appliances": {"refrigerator": "'(Checkbox_28)'", "washer": "'(Checkbox_29)'", "dryer": "'(Checkbox_30)'", "dishwasher": "'(Checkbox_31)'", "microwave": "'(Checkbox_32)'", "oven_range": "'(Checkbox_33)'", "garbage_disposal": "'(Checkbox_34)'", "wine_cooler": "'(Checkbox_35)'", "ice_maker": "'(Checkbox_36)'", "security_system": "'(Checkbox_37)'", "garage_door_openers": "'(Checkbox_38)'", "pool_equipment": "'(Checkbox_39)'", "spa_equipment": "'(Checkbox_40)'", "dock_equipment": "'(Checkbox_41)'", "outdoor_kitchen": "'(Checkbox_42)'", "generator": "'(Checkbox_43)'", "solar_panels": "'(Checkbox_44)'", "water_softener": "'(Checkbox_45)'", "reverse_osmosis": "'(Checkbox_46)'", "central_vacuum": "'(Checkbox_47)'", "intercom_system": "'(Checkbox_48)'", "sound_system": "'(Checkbox_49)'", "lighting_controls": "'(Checkbox_50)'", "irrigation_system": "'(Checkbox_51)'", "landscape_lighting": "'(Checkbox_52)'", "outdoor_speakers": "'(Checkbox_53)'", "fire_pit": "'(Checkbox_54)'", "pergola": "'(Checkbox_55)'", "gazebo": "'(Checkbox_56)'"}, "closing": {"title_insurance": "'(Checkbox_72)'", "survey_required_closing": "'(Checkbox_73)'"}}}