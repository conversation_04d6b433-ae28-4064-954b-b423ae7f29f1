{"template_info": {"pdf_name": "FloridaTemplate.pdf", "total_pages": 10, "total_fields": 129, "field_types": {"text_fields": 58, "checkboxes": 73, "number_fields": 9, "date_fields": 5, "signature_fields": 4, "initials_fields": 36, "email_fields": 1, "phone_fields": 2}}, "formdata_to_pdf_mapping": {"propertyInfo": {"sellerName": "'(Text_1)'", "buyerName": "'(Text_2)'", "streetAddress": "'(Text_3)'", "county": "'(Text_4)'", "propertyTaxId": "'(Text_5)'", "legalDescription1": "'(Text_6)'", "legalDescription2": "'(Text_7)'", "personalPropertyIncluded": "'(Text_9)'", "additionalPersonalProperty": "'(Text_10)'", "itemsExcluded": "'(Text_11)'"}, "financialInfo": {"purchasePrice": "'(Number_1)'", "initialDeposit": "'(Number_2)'", "balanceToClose": "'(Number_3)'", "closingDate": "'(Date_1)'"}, "paymentMethod": {"paymentType_cash": "'(Checkbox_8)'", "paymentType_conventional": "'(Checkbox_9)'", "paymentType_fha": "'(Checkbox_10)'", "paymentType_va": "'(Checkbox_11)'"}, "escrowInfo": {"escrowAgentName": "'(Text_12)'", "escrowAgentAddress": "'(Text_13)'"}, "partyDetails": {"sellerInitials": "'(Initials_1)'", "buyerInitials": "'(Initials_2)'", "contractDate": "'(Date_1)'"}}, "included_items_mapping": {"refrigerator": "'(Checkbox_16)'", "washer": "'(Checkbox_17)'", "dryer": "'(Checkbox_18)'", "microwave": "'(Checkbox_19)'", "dishwasher": "'(Checkbox_20)'", "security_system": "'(Checkbox_21)'"}, "contact_info_mapping": {"seller_phone": "'(US_Phone_Number_1)'", "buyer_phone": "'(US_Phone_Number_2)'", "seller_email": "'(Email_1)'", "buyer_address": "'(Text_14)'", "seller_address": "'(Text_15)'"}, "additional_fields_mapping": {"closing_costs": "'(Number_4)'", "earnest_money": "'(Number_5)'", "inspection_period": "'(Number_6)'", "financing_deadline": "'(Number_7)'", "appraisal_contingency": "'(Number_8)'", "additional_terms_1": "'(Text_16)'", "additional_terms_2": "'(Text_17)'", "additional_terms_3": "'(Text_18)'"}, "signature_fields_mapping": {"seller_signature": "'(Signature_1)'", "buyer_signature": "'(Signature_2)'", "seller_signature_date": "'(Date_2)'", "buyer_signature_date": "'(Date_3)'", "witness_signature": "'(Signature_3)'", "notary_signature": "'(Signature_4)'", "witness_date": "'(Date_4)'", "notary_date": "'(Date_5)'"}, "initials_mapping": {"seller_initials_page_1": "'(Initials_1)'", "buyer_initials_page_1": "'(Initials_2)'", "seller_initials_page_2": "'(Initials_3)'", "buyer_initials_page_2": "'(Initials_4)'", "seller_initials_page_3": "'(Initials_5)'", "buyer_initials_page_3": "'(Initials_6)'", "seller_initials_page_4": "'(Initials_7)'", "buyer_initials_page_4": "'(Initials_8)'"}, "usage_example": {"'(Text_1)'": "<PERSON>", "'(Text_2)'": "<PERSON>", "'(Text_3)'": "123 Ocean Drive, Miami Beach, FL 33139", "'(Text_4)'": "Miami-Dade", "'(Text_5)'": "12-3456-789-0123", "'(Text_6)'": "Lot 15, Block 3, SUNSET ESTATES SUBDIVISION", "'(Text_7)'": "Pool, deck, and landscaping included", "'(Text_9)'": "All kitchen appliances and window treatments", "'(Text_10)'": "Outdoor furniture and artwork excluded", "'(Number_1)'": "750000", "'(Number_2)'": "75000", "'(Date_1)'": "2025-07-15", "'(Checkbox_8)'": true, "'(Checkbox_16)'": true, "'(Checkbox_17)'": true, "'(Checkbox_18)'": true, "'(US_Phone_Number_1)'": "(*************", "'(Email_1)'": "<EMAIL>", "'(Initials_1)'": "SJ", "'(Initials_2)'": "MR"}, "field_descriptions": {"'(Text_1)'": "Seller Name - Primary seller's full legal name", "'(Text_2)'": "Buyer Name - Primary buyer's full legal name", "'(Text_3)'": "Property Street Address - Complete property address including city, state, zip", "'(Text_4)'": "County - County where property is located", "'(Text_5)'": "Property Tax ID - Official property tax identification number", "'(Text_6)'": "Legal Description Line 1 - First line of legal property description", "'(Text_7)'": "Legal Description Line 2 - Second line of legal property description", "'(Text_9)'": "Personal Property Included - Items included with sale", "'(Text_10)'": "Personal Property Excluded - Items excluded from sale", "'(Number_1)'": "Purchase Price - Total purchase price in USD", "'(Number_2)'": "Initial Deposit - Initial earnest money deposit amount", "'(Date_1)'": "Contract Date - Date the contract is signed", "'(Checkbox_8)'": "Cash Payment - Check if paying cash", "'(Checkbox_9)'": "Conventional Financing - Check if using conventional loan", "'(Checkbox_10)'": "FHA Financing - Check if using FHA loan", "'(Checkbox_11)'": "VA Financing - Check if using VA loan", "'(Checkbox_16)'": "Refrigerator Included - Check if refrigerator stays", "'(Checkbox_17)'": "Washer Included - Check if washer stays", "'(Checkbox_18)'": "Dryer Included - Check if dryer stays", "'(Checkbox_19)'": "Microwave Included - Check if microwave stays", "'(Checkbox_20)'": "Dishwasher Included - Check if dishwasher stays", "'(Checkbox_21)'": "Security System Included - Check if security system stays", "'(US_Phone_Number_1)'": "Seller Phone Number - Primary contact phone for seller", "'(US_Phone_Number_2)'": "Buyer Phone Number - Primary contact phone for buyer", "'(Email_1)'": "<PERSON><PERSON> Email - Primary email address for seller", "'(Initials_1)'": "Seller Initials - <PERSON><PERSON>'s initials for document pages", "'(Initials_2)'": "Buyer Initials - Buyer's initials for document pages", "'(Signature_1)'": "<PERSON><PERSON> Signature - <PERSON><PERSON>'s legal signature", "'(Signature_2)'": "Buyer Signature - Buyer's legal signature", "'(Date_2)'": "Seller Signature Date - Date seller signed", "'(Date_3)'": "Buyer Signature Date - Date buyer signed"}}