export interface PropertyInfo {
  sellerName: string;
  buyerName: string;
  streetAddress: string;
  county: string;
  legalDescription1: string;
  legalDescription2: string;
  propertyTaxId: string;
  personalPropertyIncluded: string;
  additionalPersonalProperty: string;
  itemsExcluded: string;
}

export interface FinancialInfo {
  purchasePrice: number;
  initialDeposit: number;
  balanceToClose: number;
  closingDate: string;
}

export interface PaymentMethod {
  paymentType: 'cash' | 'financing';
  loanType: string;
  interestRateType: string;
  financingAmount: number; // Auto-calculated
}

export interface EscrowInfo {
  escrowAgentName: string;
  escrowAgentAddress: string;
}

export interface PartyDetails {
  sellerInitials: string;
  buyerInitials: string;
  contractDate: string;
}

export interface TitleClosingLogic {
  closingOption: 'seller-designates' | 'buyer-designates';
  sellerDesignatesClosingAgent: boolean;
  buyerDesignatesClosingAgent: boolean;
}

export interface Contingencies {
  inspectionPeriod: string; // Number of days for inspection period
  financingDeadline: string; // Number of days for financing deadline
  appraisalContingency: string; // Number of days for appraisal contingency
}

export interface AutoGeneratedFields {
  countyTaxId: string; // Auto-generates based on county
  multipleBuyerInitials: string[]; // Populates all buyer initial fields
  multipleSellerInitials: string[]; // Populates all seller initial fields
  multipleDateFields: string[]; // Populates all date fields
  additionalTerms: string[]; // Split into separate PDF fields
}

export interface FormData {
  propertyInfo: PropertyInfo;
  financialInfo: FinancialInfo;
  paymentMethod: PaymentMethod;
  escrowInfo: EscrowInfo;
  partyDetails: PartyDetails;
  titleClosingLogic: TitleClosingLogic;
  contingencies?: Contingencies; // Optional contingency periods
  autoGeneratedFields: AutoGeneratedFields;
  additionalTerms: string[];
}

export const initialFormData: FormData = {
  propertyInfo: {
    sellerName: '',
    buyerName: '',
    streetAddress: '',
    county: '',
    legalDescription1: '',
    legalDescription2: '',
    propertyTaxId: '',
    personalPropertyIncluded: '',
    additionalPersonalProperty: '',
    itemsExcluded: '',
  },
  financialInfo: {
    purchasePrice: 0,
    initialDeposit: 0,
    balanceToClose: 0,
    closingDate: '',
  },
  paymentMethod: {
    paymentType: 'cash',
    loanType: '',
    interestRateType: '',
    financingAmount: 0,
  },
  escrowInfo: {
    escrowAgentName: '',
    escrowAgentAddress: '',
  },
  partyDetails: {
    sellerInitials: '',
    buyerInitials: '',
    contractDate: '',
  },
  titleClosingLogic: {
    closingOption: 'seller-designates',
    sellerDesignatesClosingAgent: false,
    buyerDesignatesClosingAgent: false,
  },
  contingencies: {
    inspectionPeriod: '',
    financingDeadline: '',
    appraisalContingency: '',
  },
  autoGeneratedFields: {
    countyTaxId: '',
    multipleBuyerInitials: [],
    multipleSellerInitials: [],
    multipleDateFields: [],
    additionalTerms: [],
  },
  additionalTerms: [''],
};