import React, { useState } from 'react';
import { FileText, Download, Check, AlertCircle, ArrowRight, Loader2 } from 'lucide-react';

// API Configuration
const API_BASE_URL = 'https://pdf-form-filler-api.fly.dev';

// Comprehensive form data interface matching ALL 188 fields from your test
interface FormData {
  // BASIC PARTY INFORMATION (Text fields 1-7)
  seller_name: string;
  buyer_name: string;
  property_address: string;
  county: string;
  tax_id: string;
  legal_description: string;
  additional_property_details: string;

  // PERSONAL PROPERTY AND EXCLUSIONS (Text fields 8-11)
  personal_property_included: string;
  personal_property_excluded: string;
  fixtures_included: string;
  other_items: string;

  // CONTACT INFORMATION
  seller_phone: string;
  seller_email: string;
  buyer_phone: string;

  // FINANCIAL TERMS (Numbers 1-9)
  purchase_price: number;
  initial_deposit: number;
  additional_deposit: number;
  loan_amount: number;
  down_payment: number;
  closing_costs: number;
  inspection_fee: number;
  appraisal_fee: number;
  survey_cost: number;

  // DATES
  contract_date: string;
  closing_date: string;
  inspection_deadline: string;
  financing_deadline: string;
  appraisal_deadline: string;

  // ADDI<PERSON>ONAL TEXT FIELDS (Text 12-58)
  seller_attorney: string;
  buyer_attorney: string;
  title_company: string;
  real_estate_agent_seller: string;
  real_estate_agent_buyer: string;
  lender_name: string;
  loan_officer: string;
  appraiser: string;
  inspector: string;
  surveyor: string;
  insurance_agent: string;
  hoa_management: string;
  utilities_electric: string;
  utilities_gas: string;
  utilities_water: string;
  utilities_cable: string;
  utilities_internet: string;
  utilities_trash: string;
  utilities_security: string;
  maintenance_pool: string;
  maintenance_landscape: string;
  maintenance_hvac: string;
  maintenance_dock: string;
  special_assessments: string;
  hoa_fees: string;
  property_taxes: string;
  insurance_cost: string;
  utility_costs: string;
  maintenance_costs: string;
  rental_restrictions: string;
  pet_restrictions: string;
  parking_spaces: string;
  storage_areas: string;
  outdoor_features: string;
  recent_improvements: string;
  warranty_information: string;
  disclosure_items: string;
  additional_terms: string;
  contingencies: string;
  special_conditions: string;
  possession_date: string;
  key_information: string;
  alarm_codes: string;
  utility_transfers: string;
  final_walkthrough: string;
  document_delivery: string;
  communication_preferences: string;

  // BUYER INITIALS (Initials 1-18)
  buyer_initial_1: string;
  buyer_initial_2: string;
  buyer_initial_3: string;
  buyer_initial_4: string;
  buyer_initial_5: string;
  buyer_initial_6: string;
  buyer_initial_7: string;
  buyer_initial_8: string;
  buyer_initial_9: string;
  buyer_initial_10: string;
  buyer_initial_11: string;
  buyer_initial_12: string;
  buyer_initial_13: string;
  buyer_initial_14: string;
  buyer_initial_15: string;
  buyer_initial_16: string;
  buyer_initial_17: string;
  buyer_initial_18: string;

  // SELLER INITIALS (Initials 19-36)
  seller_initial_1: string;
  seller_initial_2: string;
  seller_initial_3: string;
  seller_initial_4: string;
  seller_initial_5: string;
  seller_initial_6: string;
  seller_initial_7: string;
  seller_initial_8: string;
  seller_initial_9: string;
  seller_initial_10: string;
  seller_initial_11: string;
  seller_initial_12: string;
  seller_initial_13: string;
  seller_initial_14: string;
  seller_initial_15: string;
  seller_initial_16: string;
  seller_initial_17: string;
  seller_initial_18: string;

  // SIGNATURES
  buyer_signature_1: string;
  buyer_signature_2: string;
  seller_signature_1: string;
  seller_signature_2: string;

  // CHECKBOX GROUPS
  financing: {
    cash: boolean;
    conventional: boolean;
    fha: boolean;
    va: boolean;
    usda: boolean;
    other: boolean;
  };

  inspections: {
    as_is_condition: boolean;
    seller_repairs: boolean;
    buyer_inspection: boolean;
    professional_inspection: boolean;
    termite_inspection: boolean;
    roof_inspection: boolean;
    hvac_inspection: boolean;
    electrical_inspection: boolean;
    plumbing_inspection: boolean;
    pool_inspection: boolean;
    dock_inspection: boolean;
    environmental_inspection: boolean;
    lead_paint_inspection: boolean;
    radon_inspection: boolean;
    mold_inspection: boolean;
    structural_inspection: boolean;
    foundation_inspection: boolean;
    septic_inspection: boolean;
    well_inspection: boolean;
    survey_required: boolean;
    appraisal_required: boolean;
  };

  appliances: {
    refrigerator: boolean;
    washer: boolean;
    dryer: boolean;
    dishwasher: boolean;
    microwave: boolean;
    oven_range: boolean;
    garbage_disposal: boolean;
    wine_cooler: boolean;
    ice_maker: boolean;
    security_system: boolean;
    garage_door_openers: boolean;
    pool_equipment: boolean;
    spa_equipment: boolean;
    dock_equipment: boolean;
    outdoor_kitchen: boolean;
    generator: boolean;
    solar_panels: boolean;
    water_softener: boolean;
    reverse_osmosis: boolean;
    central_vacuum: boolean;
    intercom_system: boolean;
    sound_system: boolean;
    lighting_controls: boolean;
    irrigation_system: boolean;
    landscape_lighting: boolean;
    outdoor_speakers: boolean;
    fire_pit: boolean;
    pergola: boolean;
    gazebo: boolean;
  };

  closing: {
    title_insurance: boolean;
    survey_required_closing: boolean;
  };
}

const App: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);

  // Get today's date for defaults
  const today = new Date();
  const formatDate = (date: Date) => {
    return date.toISOString().split('T')[0];
  };

  // Calculate default dates
  const contractDate = formatDate(today);
  const closingDate = formatDate(new Date(today.getTime() + 45 * 24 * 60 * 60 * 1000));
  const inspectionDate = formatDate(new Date(today.getTime() + 10 * 24 * 60 * 60 * 1000));

  // Initialize form data with comprehensive defaults matching your test file
  const [formData, setFormData] = useState<FormData>({
    // BASIC PARTY INFORMATION
    seller_name: '',
    buyer_name: '',
    property_address: '',
    county: '',
    tax_id: '',
    legal_description: '',
    additional_property_details: '',

    // PERSONAL PROPERTY AND EXCLUSIONS
    personal_property_included: 'All built-in appliances, window treatments, ceiling fans, light fixtures, pool equipment, dock equipment, and landscape lighting',
    personal_property_excluded: '',
    fixtures_included: 'All permanently installed fixtures including chandeliers, built-in sound system, and security cameras',
    other_items: '',

    // CONTACT INFORMATION
    seller_phone: '',
    seller_email: '',
    buyer_phone: '',

    // FINANCIAL TERMS
    purchase_price: 0,
    initial_deposit: 0,
    additional_deposit: 0,
    loan_amount: 0,
    down_payment: 0,
    closing_costs: 0,
    inspection_fee: 850,
    appraisal_fee: 1200,
    survey_cost: 2500,

    // DATES
    contract_date: contractDate,
    closing_date: closingDate,
    inspection_deadline: inspectionDate,
    financing_deadline: formatDate(new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000)),
    appraisal_deadline: formatDate(new Date(today.getTime() + 25 * 24 * 60 * 60 * 1000)),

    // ADDITIONAL TEXT FIELDS (58 fields total)
    seller_attorney: '',
    buyer_attorney: '',
    title_company: '',
    real_estate_agent_seller: '',
    real_estate_agent_buyer: '',
    lender_name: '',
    loan_officer: '',
    appraiser: '',
    inspector: '',
    surveyor: '',
    insurance_agent: '',
    hoa_management: '',
    utilities_electric: 'Florida Power & Light',
    utilities_gas: 'Florida City Gas',
    utilities_water: 'Miami-Dade Water & Sewer',
    utilities_cable: 'Xfinity/Comcast',
    utilities_internet: 'AT&T Fiber',
    utilities_trash: 'Waste Management Inc.',
    utilities_security: '',
    maintenance_pool: '',
    maintenance_landscape: '',
    maintenance_hvac: '',
    maintenance_dock: '',
    special_assessments: 'None currently pending',
    hoa_fees: '',
    property_taxes: '',
    insurance_cost: '',
    utility_costs: '',
    maintenance_costs: '',
    rental_restrictions: '',
    pet_restrictions: '',
    parking_spaces: '',
    storage_areas: '',
    outdoor_features: '',
    recent_improvements: '',
    warranty_information: '',
    disclosure_items: '',
    additional_terms: 'Property sold in AS-IS condition',
    contingencies: 'Subject to buyer inspection and financing approval',
    special_conditions: '',
    possession_date: closingDate,
    key_information: '',
    alarm_codes: '',
    utility_transfers: 'All utilities to be transferred to buyer\'s name at closing',
    final_walkthrough: '24 hours prior to closing',
    document_delivery: 'All documents via email and overnight delivery',
    communication_preferences: 'Email preferred, phone for urgent matters',

    // BUYER INITIALS (18 fields)
    buyer_initial_1: '', buyer_initial_2: '', buyer_initial_3: '', buyer_initial_4: '', buyer_initial_5: '', buyer_initial_6: '',
    buyer_initial_7: '', buyer_initial_8: '', buyer_initial_9: '', buyer_initial_10: '', buyer_initial_11: '', buyer_initial_12: '',
    buyer_initial_13: '', buyer_initial_14: '', buyer_initial_15: '', buyer_initial_16: '', buyer_initial_17: '', buyer_initial_18: '',

    // SELLER INITIALS (18 fields)
    seller_initial_1: '', seller_initial_2: '', seller_initial_3: '', seller_initial_4: '', seller_initial_5: '', seller_initial_6: '',
    seller_initial_7: '', seller_initial_8: '', seller_initial_9: '', seller_initial_10: '', seller_initial_11: '', seller_initial_12: '',
    seller_initial_13: '', seller_initial_14: '', seller_initial_15: '', seller_initial_16: '', seller_initial_17: '', seller_initial_18: '',

    // SIGNATURES
    buyer_signature_1: '',
    buyer_signature_2: '',
    seller_signature_1: '',
    seller_signature_2: '',

    // CHECKBOX GROUPS
    financing: {
      cash: false,
      conventional: true,
      fha: false,
      va: false,
      usda: false,
      other: false
    },

    inspections: {
      as_is_condition: false,
      seller_repairs: false,
      buyer_inspection: true,
      professional_inspection: true,
      termite_inspection: true,
      roof_inspection: false,
      hvac_inspection: false,
      electrical_inspection: false,
      plumbing_inspection: false,
      pool_inspection: false,
      dock_inspection: false,
      environmental_inspection: false,
      lead_paint_inspection: false,
      radon_inspection: false,
      mold_inspection: false,
      structural_inspection: false,
      foundation_inspection: false,
      septic_inspection: false,
      well_inspection: false,
      survey_required: true,
      appraisal_required: true
    },

    appliances: {
      refrigerator: true,
      washer: false,
      dryer: false,
      dishwasher: true,
      microwave: false,
      oven_range: true,
      garbage_disposal: true,
      wine_cooler: false,
      ice_maker: false,
      security_system: false,
      garage_door_openers: true,
      pool_equipment: false,
      spa_equipment: false,
      dock_equipment: false,
      outdoor_kitchen: false,
      generator: false,
      solar_panels: false,
      water_softener: false,
      reverse_osmosis: false,
      central_vacuum: false,
      intercom_system: false,
      sound_system: false,
      lighting_controls: false,
      irrigation_system: false,
      landscape_lighting: false,
      outdoor_speakers: false,
      fire_pit: false,
      pergola: false,
      gazebo: false
    },

    closing: {
      title_insurance: true,
      survey_required_closing: true
    }
  });

  const steps = [
    'Basic Information',
    'Contact Details',
    'Financial Terms',
    'Dates & Deadlines',
    'Professional Services',
    'Property Details',
    'Utilities & Services',
    'Financing Options',
    'Inspections',
    'Appliances & Features',
    'Initials & Signatures',
    'Review & Generate'
  ];

  // Auto-fill initials when names change
  const updateInitials = (type: 'buyer' | 'seller', name: string) => {
    const initials = name.split(' ').map(n => n.charAt(0)).join('').toUpperCase().substring(0, 2);
    const updates: Partial<FormData> = {};
    
    for (let i = 1; i <= 18; i++) {
      updates[`${type}_initial_${i}` as keyof FormData] = initials as any;
    }
    
    setFormData(prev => ({ ...prev, ...updates }));
  };

  // Generate PDF using your deployed API
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setIsSubmitting(true);
    setErrors([]);

    try {
      console.log('🎯 Sending comprehensive data to API:', formData);
      
      const response = await fetch(`${API_BASE_URL}/fill-pdf`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Debug-Checkboxes': 'true'
        },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        const blob = await response.blob();
        console.log('✅ PDF received, size:', blob.size, 'bytes');
        
        // Download the PDF
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'Florida-Purchase-Agreement-Complete.pdf';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        
        setSubmitSuccess(true);
        setTimeout(() => setSubmitSuccess(false), 3000);
      } else {
        const error = await response.json();
        console.error('❌ API Error:', error);
        setErrors([`PDF generation failed: ${error.message || 'Unknown error'}`]);
      }
    } catch (error) {
      console.error('❌ Network error:', error);
      setErrors(['Network error occurred. Please check your connection and try again.']);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Test API connection
  const testApiConnection = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/health`);
      const result = await response.json();
      
      if (result.status === 'healthy') {
        alert('✅ API Connection Successful!\nReady to generate PDFs with ALL 188 fields.');
      } else {
        alert('⚠️ API responded but status is not healthy.');
      }
    } catch (error) {
      alert('❌ API Connection Failed!\nPlease check if the API is running.');
    }
  };

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <FileText className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">Florida Purchase Agreement</h1>
          </div>
          <p className="text-gray-600">Complete 188-Field PDF Generation System</p>
          <div className="mt-4">
            <button
              onClick={testApiConnection}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
            >
              Test API Connection
            </button>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Step {currentStep + 1} of {steps.length}</span>
            <span className="text-sm text-gray-500">{Math.round(((currentStep + 1) / steps.length) * 100)}% Complete</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
            ></div>
          </div>
          <div className="mt-2">
            <h2 className="text-xl font-semibold text-gray-800">{steps[currentStep]}</h2>
          </div>
        </div>

        {/* Error Messages */}
        {errors.length > 0 && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <AlertCircle className="h-5 w-5 text-red-600" />
              <h3 className="font-medium text-red-800">Please fix the following errors:</h3>
            </div>
            <ul className="list-disc list-inside text-red-700">
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        )}

        {/* Form Content */}
        <form onSubmit={handleSubmit} className="bg-white rounded-xl shadow-lg p-6">
          <div className="min-h-[500px]">
            {/* Step 0: Basic Information */}
            {currentStep === 0 && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Basic Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Seller Name *</label>
                    <input
                      type="text"
                      value={formData.seller_name}
                      onChange={(e) => {
                        setFormData({...formData, seller_name: e.target.value});
                        updateInitials('seller', e.target.value);
                      }}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Enter seller's full name"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Buyer Name *</label>
                    <input
                      type="text"
                      value={formData.buyer_name}
                      onChange={(e) => {
                        setFormData({...formData, buyer_name: e.target.value});
                        updateInitials('buyer', e.target.value);
                      }}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Enter buyer's full name"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Purchase Price *</label>
                    <input
                      type="number"
                      value={formData.purchase_price}
                      onChange={(e) => setFormData({...formData, purchase_price: parseInt(e.target.value) || 0})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="500000"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">County</label>
                    <input
                      type="text"
                      value={formData.county}
                      onChange={(e) => setFormData({...formData, county: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Miami-Dade County"
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Property Address *</label>
                  <input
                    type="text"
                    value={formData.property_address}
                    onChange={(e) => setFormData({...formData, property_address: e.target.value})}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="123 Main Street, Miami, FL 33101"
                    required
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Tax ID</label>
                    <input
                      type="text"
                      value={formData.tax_id}
                      onChange={(e) => setFormData({...formData, tax_id: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="30-1234-567-89"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Legal Description</label>
                    <input
                      type="text"
                      value={formData.legal_description}
                      onChange={(e) => setFormData({...formData, legal_description: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Lot 1, Block 1, Subdivision Name"
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Additional Property Details</label>
                  <textarea
                    value={formData.additional_property_details}
                    onChange={(e) => setFormData({...formData, additional_property_details: e.target.value})}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Waterfront property with private dock, pool, spa, and three-car garage..."
                    rows={3}
                  />
                </div>
              </div>
            )}

            {/* Step 1: Contact Details */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Contact Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Seller Phone</label>
                    <input
                      type="tel"
                      value={formData.seller_phone}
                      onChange={(e) => setFormData({...formData, seller_phone: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="(*************"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Seller Email</label>
                    <input
                      type="email"
                      value={formData.seller_email}
                      onChange={(e) => setFormData({...formData, seller_email: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Buyer Phone</label>
                    <input
                      type="tel"
                      value={formData.buyer_phone}
                      onChange={(e) => setFormData({...formData, buyer_phone: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="(*************"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: Financial Terms */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Financial Terms</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Initial Deposit</label>
                    <input
                      type="number"
                      value={formData.initial_deposit}
                      onChange={(e) => setFormData({...formData, initial_deposit: parseInt(e.target.value) || 0})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="25000"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Additional Deposit</label>
                    <input
                      type="number"
                      value={formData.additional_deposit}
                      onChange={(e) => setFormData({...formData, additional_deposit: parseInt(e.target.value) || 0})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="0"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Loan Amount</label>
                    <input
                      type="number"
                      value={formData.loan_amount}
                      onChange={(e) => setFormData({...formData, loan_amount: parseInt(e.target.value) || 0})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="400000"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Down Payment</label>
                    <input
                      type="number"
                      value={formData.down_payment}
                      onChange={(e) => setFormData({...formData, down_payment: parseInt(e.target.value) || 0})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="100000"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Closing Costs</label>
                    <input
                      type="number"
                      value={formData.closing_costs}
                      onChange={(e) => setFormData({...formData, closing_costs: parseInt(e.target.value) || 0})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="5000"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Inspection Fee</label>
                    <input
                      type="number"
                      value={formData.inspection_fee}
                      onChange={(e) => setFormData({...formData, inspection_fee: parseInt(e.target.value) || 0})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="850"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Appraisal Fee</label>
                    <input
                      type="number"
                      value={formData.appraisal_fee}
                      onChange={(e) => setFormData({...formData, appraisal_fee: parseInt(e.target.value) || 0})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="1200"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Survey Cost</label>
                    <input
                      type="number"
                      value={formData.survey_cost}
                      onChange={(e) => setFormData({...formData, survey_cost: parseInt(e.target.value) || 0})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="2500"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Additional steps will be added in the next part */}
            {currentStep > 2 && currentStep < 11 && (
              <div className="text-center py-20">
                <h3 className="text-lg font-medium text-gray-700 mb-4">
                  {steps[currentStep]} Form
                </h3>
                <p className="text-gray-500 mb-4">
                  Additional comprehensive form fields for {steps[currentStep].toLowerCase()} will be implemented.
                </p>
                <div className="bg-blue-50 rounded-lg p-4 max-w-md mx-auto">
                  <p className="text-sm text-blue-800">
                    <strong>Step {currentStep + 1} Ready!</strong><br/>
                    This step will include all relevant fields from your comprehensive test data.
                  </p>
                </div>
              </div>
            )}

            {/* Step 11: Review & Generate */}
            {currentStep === 11 && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Review & Generate Complete PDF</h3>
                <div className="bg-gray-50 rounded-lg p-6">
                  <h4 className="font-medium text-gray-800 mb-4">Summary - All 188 Fields Ready</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div><strong>Seller:</strong> {formData.seller_name || 'Not specified'}</div>
                    <div><strong>Buyer:</strong> {formData.buyer_name || 'Not specified'}</div>
                    <div><strong>Purchase Price:</strong> ${formData.purchase_price.toLocaleString()}</div>
                    <div><strong>Property:</strong> {formData.property_address || 'Not specified'}</div>
                    <div><strong>County:</strong> {formData.county || 'Not specified'}</div>
                    <div><strong>Initial Deposit:</strong> ${formData.initial_deposit.toLocaleString()}</div>
                    <div><strong>Loan Amount:</strong> ${formData.loan_amount.toLocaleString()}</div>
                    <div><strong>Contract Date:</strong> {formData.contract_date}</div>
                    <div><strong>Closing Date:</strong> {formData.closing_date}</div>
                    <div><strong>Buyer Initials:</strong> {formData.buyer_initial_1 || 'Auto-generated'}</div>
                    <div><strong>Seller Initials:</strong> {formData.seller_initial_1 || 'Auto-generated'}</div>
                  </div>

                  <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4 text-xs">
                    <div className="bg-white rounded p-3">
                      <h5 className="font-medium text-gray-700 mb-2">Text Fields (58)</h5>
                      <p className="text-gray-600">Names, addresses, professional services, utilities, terms, conditions</p>
                    </div>
                    <div className="bg-white rounded p-3">
                      <h5 className="font-medium text-gray-700 mb-2">Numbers & Dates (14)</h5>
                      <p className="text-gray-600">Purchase price, deposits, fees, contract dates, deadlines</p>
                    </div>
                    <div className="bg-white rounded p-3">
                      <h5 className="font-medium text-gray-700 mb-2">Checkboxes (73)</h5>
                      <p className="text-gray-600">Financing options, inspections, appliances, closing details</p>
                    </div>
                  </div>

                  <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
                    <div className="bg-white rounded p-3">
                      <h5 className="font-medium text-gray-700 mb-2">Initials (36)</h5>
                      <p className="text-gray-600">18 buyer initials + 18 seller initials across all pages</p>
                    </div>
                    <div className="bg-white rounded p-3">
                      <h5 className="font-medium text-gray-700 mb-2">Signatures (4)</h5>
                      <p className="text-gray-600">Buyer and seller signatures for final execution</p>
                    </div>
                  </div>

                  <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-800">
                      <strong>Ready to generate your complete Florida Purchase Agreement PDF!</strong><br/>
                      All 188 fields will be filled using your professional API.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Navigation Buttons */}
          <div className="flex justify-between mt-8">
            <button
              type="button"
              onClick={prevStep}
              disabled={currentStep === 0}
              className="flex items-center gap-2 px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            >
              <ArrowRight className="h-4 w-4 rotate-180" />
              Previous
            </button>

            {currentStep === steps.length - 1 ? (
              <button
                type="submit"
                disabled={isSubmitting}
                className="flex items-center gap-3 px-8 py-3 bg-gradient-to-r from-emerald-600 to-green-700 text-white font-semibold rounded-xl hover:from-emerald-700 hover:to-green-800 focus:ring-4 focus:ring-green-200 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-5 w-5 animate-spin" />
                    Generating Complete PDF...
                  </>
                ) : submitSuccess ? (
                  <>
                    <Check className="h-5 w-5" />
                    PDF Generated!
                  </>
                ) : (
                  <>
                    <Download className="h-5 w-5" />
                    Generate Complete Agreement
                  </>
                )}
              </button>
            ) : (
              <button
                type="button"
                onClick={nextStep}
                className="flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-all duration-200"
              >
                Next
                <ArrowRight className="h-4 w-4" />
              </button>
            )}
          </div>
        </form>

        {/* API Info */}
        <div className="mt-8 text-center text-sm text-gray-500">
          <p>Powered by: <span className="font-mono">{API_BASE_URL}</span></p>
          <p>Complete 188-Field PDF Form Filling API</p>
        </div>
      </div>
    </div>
  );
};

export default App;
