// Simple validation utilities for form fields
// This replaces the complex PDF mapping system with basic validation

export interface ValidationResult {
  sanitizedValue: string;
  isValid: boolean;
  error?: string;
}

export const validateTaxId = (value: string): ValidationResult => {
  // Basic tax ID validation - remove extra spaces and validate format
  const sanitized = value.trim().replace(/\s+/g, ' ');
  
  // Florida tax ID format: XX-XXXX-XXX-XX
  const taxIdPattern = /^\d{2}-\d{4}-\d{3}-\d{2}$/;
  const isValid = taxIdPattern.test(sanitized) || sanitized === '';
  
  return {
    sanitizedValue: sanitized,
    isValid,
    error: !isValid && sanitized !== '' ? 'Tax ID should be in format: XX-XXXX-XXX-XX' : undefined
  };
};

export const validateAddress = (value: string): ValidationResult => {
  // Basic address validation - clean up spacing and basic format check
  const sanitized = value.trim().replace(/\s+/g, ' ');
  
  // Basic address validation - should have some numbers and letters
  const hasNumbers = /\d/.test(sanitized);
  const hasLetters = /[a-zA-Z]/.test(sanitized);
  const isValid = (hasNumbers && hasLetters) || sanitized === '';
  
  return {
    sanitizedValue: sanitized,
    isValid,
    error: !isValid && sanitized !== '' ? 'Please enter a valid address' : undefined
  };
};

export const validateName = (value: string): ValidationResult => {
  // Basic name validation - clean up spacing and check for valid characters
  const sanitized = value.trim().replace(/\s+/g, ' ');
  
  // Name should only contain letters, spaces, hyphens, and apostrophes
  const namePattern = /^[a-zA-Z\s\-']+$/;
  const isValid = namePattern.test(sanitized) || sanitized === '';
  
  return {
    sanitizedValue: sanitized,
    isValid,
    error: !isValid && sanitized !== '' ? 'Name should only contain letters, spaces, hyphens, and apostrophes' : undefined
  };
};

export const validateEmail = (value: string): ValidationResult => {
  const sanitized = value.trim().toLowerCase();
  
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const isValid = emailPattern.test(sanitized) || sanitized === '';
  
  return {
    sanitizedValue: sanitized,
    isValid,
    error: !isValid && sanitized !== '' ? 'Please enter a valid email address' : undefined
  };
};

export const validatePhone = (value: string): ValidationResult => {
  // Clean up phone number - remove all non-digits
  const digitsOnly = value.replace(/\D/g, '');
  
  // Format as (XXX) XXX-XXXX if 10 digits
  let sanitized = value.trim();
  if (digitsOnly.length === 10) {
    sanitized = `(${digitsOnly.slice(0, 3)}) ${digitsOnly.slice(3, 6)}-${digitsOnly.slice(6)}`;
  }
  
  const isValid = digitsOnly.length === 10 || sanitized === '';
  
  return {
    sanitizedValue: sanitized,
    isValid,
    error: !isValid && sanitized !== '' ? 'Please enter a valid 10-digit phone number' : undefined
  };
};

export const validateCurrency = (value: string): ValidationResult => {
  // Remove currency symbols and clean up
  const cleaned = value.replace(/[$,]/g, '').trim();
  const sanitized = cleaned;
  
  const isValid = /^\d*\.?\d*$/.test(cleaned) || cleaned === '';
  
  return {
    sanitizedValue: sanitized,
    isValid,
    error: !isValid && sanitized !== '' ? 'Please enter a valid amount' : undefined
  };
};

export const validateDate = (value: string): ValidationResult => {
  const sanitized = value.trim();
  
  // Basic date validation - check if it's a valid date format
  const isValid = !isNaN(Date.parse(sanitized)) || sanitized === '';
  
  return {
    sanitizedValue: sanitized,
    isValid,
    error: !isValid && sanitized !== '' ? 'Please enter a valid date' : undefined
  };
};

// Format data for your new API
export const formatDataForNewAPI = (formData: any) => {
  // This function converts your form data to the format expected by your deployed API
  // Based on your comprehensive_test.py file structure
  
  return {
    // Basic party information
    seller_name: formData.propertyInfo?.sellerName || '',
    buyer_name: formData.propertyInfo?.buyerName || '',
    property_address: formData.propertyInfo?.streetAddress || '',
    county: formData.propertyInfo?.county || '',
    tax_id: formData.propertyInfo?.propertyTaxId || '',
    legal_description: formData.propertyInfo?.legalDescription || '',
    
    // Contact information
    seller_phone: formData.partyDetails?.sellerPhone || '',
    seller_email: formData.partyDetails?.sellerEmail || '',
    buyer_phone: formData.partyDetails?.buyerPhone || '',
    
    // Financial information
    purchase_price: formData.financialInfo?.purchasePrice || 0,
    initial_deposit: formData.financialInfo?.initialDeposit || 0,
    additional_deposit: formData.financialInfo?.additionalDeposit || 0,
    loan_amount: formData.financialInfo?.loanAmount || 0,
    down_payment: formData.financialInfo?.downPayment || 0,
    closing_costs: formData.financialInfo?.closingCosts || 0,
    
    // Dates
    contract_date: formData.financialDates?.contractDate || '',
    closing_date: formData.financialDates?.closingDate || '',
    inspection_deadline: formData.financialDates?.inspectionDeadline || '',
    financing_deadline: formData.financialDates?.financingDeadline || '',
    appraisal_deadline: formData.financialDates?.appraisalDeadline || '',
    
    // Professional services
    seller_attorney: formData.titleClosing?.sellerAttorney || '',
    buyer_attorney: formData.titleClosing?.buyerAttorney || '',
    title_company: formData.titleClosing?.titleCompany || '',
    real_estate_agent_seller: formData.partyDetails?.sellerAgent || '',
    real_estate_agent_buyer: formData.partyDetails?.buyerAgent || '',
    lender_name: formData.escrowInfo?.lenderName || '',
    
    // Property details
    personal_property_included: formData.propertyDetails?.personalPropertyIncluded || '',
    personal_property_excluded: formData.propertyDetails?.personalPropertyExcluded || '',
    fixtures_included: formData.propertyDetails?.fixturesIncluded || '',
    additional_terms: formData.additionalTerms?.additionalTerms || '',
    contingencies: formData.additionalTerms?.contingencies || '',
    special_conditions: formData.additionalTerms?.specialConditions || '',
    
    // Financing options
    financing: {
      cash: formData.paymentMethod?.paymentType === 'cash',
      conventional: formData.paymentMethod?.paymentType === 'conventional',
      fha: formData.paymentMethod?.paymentType === 'fha',
      va: formData.paymentMethod?.paymentType === 'va',
      usda: formData.paymentMethod?.paymentType === 'usda',
      other: formData.paymentMethod?.paymentType === 'other'
    },
    
    // Inspections (default values)
    inspections: {
      as_is_condition: formData.propertyDetails?.asIsCondition || false,
      buyer_inspection: true,
      professional_inspection: true,
      termite_inspection: true,
      roof_inspection: false,
      hvac_inspection: false,
      electrical_inspection: false,
      plumbing_inspection: false,
      pool_inspection: false,
      environmental_inspection: false,
      lead_paint_inspection: false,
      radon_inspection: false,
      mold_inspection: false,
      structural_inspection: false,
      foundation_inspection: false,
      septic_inspection: false,
      well_inspection: false,
      survey_required: true,
      appraisal_required: true
    },
    
    // Appliances (default values)
    appliances: {
      refrigerator: true,
      washer: false,
      dryer: false,
      dishwasher: true,
      microwave: false,
      oven_range: true,
      garbage_disposal: true,
      wine_cooler: false,
      ice_maker: false,
      security_system: false,
      garage_door_openers: true,
      pool_equipment: false,
      spa_equipment: false,
      generator: false,
      solar_panels: false,
      water_softener: false,
      central_vacuum: false,
      intercom_system: false,
      sound_system: false,
      lighting_controls: false,
      irrigation_system: false,
      landscape_lighting: false,
      fire_pit: false,
      pergola: false,
      gazebo: false
    },
    
    // Closing details
    closing: {
      title_insurance: true,
      survey_required_closing: true
    }
  };
};
