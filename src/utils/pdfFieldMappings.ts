import { FormData } from '../types/FormTypes';
import {
  validateTaxId,
  validateAddress,
  validateCurrency,
  validateDate,
  validateText,
  validateName,
  validateInitials,
  validateLegalDescription,
  ValidationResult
} from './fieldValidation';
import {
  mapFormDataToFloridaTemplate,
  validateRequiredFields,
  formatCurrencyForPDF,
  formatDateForPDF
} from './floridaTemplateMappings';

// Complete PDF field mapping with validation
export interface PDFFieldMapping {
  [key: string]: {
    value: any;
    validation: ValidationResult;
  };
}

// Format currency for display
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

// Format date for PDF (readable format)
const formatDate = (dateString: string): string => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

// ===== DATA VALIDATION AND PREPARATION FUNCTIONS =====

// Validate and prepare person names (seller/buyer)
const validateAndPrepareName = (name: string, role: string): string => {
  console.log(`🔍 Validating ${role} name: "${name}"`);

  if (!name || typeof name !== 'string') {
    console.log(`❌ ${role} name is empty or invalid`);
    return '';
  }

  const cleanName = name.trim().toUpperCase();

  // Check for invalid characters (numbers, special chars except spaces, hyphens, apostrophes)
  if (/[0-9$@#%^&*()+={}[\]|\\:";?/<>]/.test(cleanName)) {
    console.log(`❌ ${role} name contains invalid characters: "${cleanName}"`);
    return cleanName.replace(/[0-9$@#%^&*()+={}[\]|\\:";?/<>]/g, '');
  }

  console.log(`✅ ${role} name validated: "${cleanName}"`);
  return cleanName;
};

// Validate and prepare street address
const validateAndPrepareAddress = (address: string): string => {
  console.log(`🔍 Validating address: "${address}"`);

  if (!address || typeof address !== 'string') {
    console.log(`❌ Address is empty or invalid`);
    return '';
  }

  const cleanAddress = address.trim();

  // Check if address contains currency symbols (common error)
  if (/\$|USD|EUR|GBP/.test(cleanAddress)) {
    console.log(`❌ Address contains currency symbols: "${cleanAddress}"`);
    return cleanAddress.replace(/\$|USD|EUR|GBP/g, '').trim();
  }

  // Check if address contains tax ID patterns (common error)
  if (/^\d{2}-\d{7}$/.test(cleanAddress)) {
    console.log(`❌ Address appears to be a tax ID: "${cleanAddress}"`);
    return '';
  }

  console.log(`✅ Address validated: "${cleanAddress}"`);
  return cleanAddress;
};

// Validate and prepare county name
const validateAndPrepareCounty = (county: string): string => {
  console.log(`🔍 Validating county: "${county}"`);

  if (!county || typeof county !== 'string') {
    console.log(`❌ County is empty or invalid`);
    return '';
  }

  const cleanCounty = county.trim();

  // Remove "County" suffix if present (we'll add it back with "Florida")
  const countyName = cleanCounty.replace(/\s+County\s*$/i, '').trim();

  // Check for invalid patterns
  if (/\$|^\d{2}-\d{7}$/.test(countyName)) {
    console.log(`❌ County contains invalid data: "${countyName}"`);
    return '';
  }

  const formattedCounty = `${countyName}, Florida`;
  console.log(`✅ County validated and formatted: "${formattedCounty}"`);
  return formattedCounty;
};

// Validate and prepare tax ID
const validateAndPrepareTaxId = (taxId: string): string => {
  console.log(`🔍 Validating tax ID: "${taxId}"`);

  if (!taxId || typeof taxId !== 'string') {
    console.log(`❌ Tax ID is empty or invalid`);
    return '';
  }

  const cleanTaxId = taxId.trim();

  // Florida property tax IDs can have various formats:
  // XX-XXXXXXX (2-7 digits) - simple format
  // XX-XXXX-XXX-XXXX (2-4-3-4 digits) - detailed format like 02-3204-001-0010
  // Just validate that it contains numbers and dashes in a reasonable pattern

  // Check if it contains only numbers, dashes, and reasonable length
  if (!/^[\d-]+$/.test(cleanTaxId)) {
    console.log(`❌ Tax ID contains invalid characters: "${cleanTaxId}"`);
    return '';
  }

  // Check if it has at least some numbers
  const numbersOnly = cleanTaxId.replace(/\D/g, '');
  if (numbersOnly.length < 5) {
    console.log(`❌ Tax ID too short: "${cleanTaxId}" (needs at least 5 digits)`);
    return '';
  }

  if (numbersOnly.length > 15) {
    console.log(`❌ Tax ID too long: "${cleanTaxId}" (max 15 digits)`);
    return '';
  }

  console.log(`✅ Tax ID validated: "${cleanTaxId}"`);
  return cleanTaxId;
};

// Validate and prepare currency amounts
const validateAndPrepareCurrency = (amount: number, fieldName: string): string => {
  console.log(`🔍 Validating ${fieldName}: ${amount}`);

  if (typeof amount !== 'number' || isNaN(amount) || amount < 0) {
    console.log(`❌ ${fieldName} is invalid: ${amount}`);
    return '$0';
  }

  const formatted = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);

  console.log(`✅ ${fieldName} validated and formatted: "${formatted}"`);
  return formatted;
};

// Validate and prepare date formatting
const validateAndPrepareDate = (date: Date | string, fieldName: string): string => {
  console.log(`🔍 Validating ${fieldName}: ${date} (type: ${typeof date})`);

  let dateObj: Date;

  // Handle both string and Date inputs
  if (typeof date === 'string') {
    dateObj = new Date(date);
  } else if (date instanceof Date) {
    dateObj = date;
  } else {
    console.log(`❌ ${fieldName} is invalid type: ${typeof date}`);
    return '';
  }

  // Check if the date is valid
  if (isNaN(dateObj.getTime())) {
    console.log(`❌ ${fieldName} is invalid date: ${date}`);
    return '';
  }

  const formatted = dateObj.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  console.log(`✅ ${fieldName} validated and formatted: "${formatted}"`);
  return formatted;
};

// Validate and prepare initials
const validateAndPrepareInitials = (initials: string, role: string): string => {
  console.log(`🔍 Validating ${role} initials: "${initials}"`);

  if (!initials || typeof initials !== 'string') {
    console.log(`❌ ${role} initials are empty or invalid`);
    return '';
  }

  const cleanInitials = initials.trim().toUpperCase().replace(/[^A-Z]/g, '');

  if (cleanInitials.length === 0 || cleanInitials.length > 4) {
    console.log(`❌ ${role} initials are invalid length: "${cleanInitials}"`);
    return '';
  }

  console.log(`✅ ${role} initials validated: "${cleanInitials}"`);
  return cleanInitials;
};

// NEW: Main function to create validated PDF field mappings for FloridaTemplate.pdf
export const createFloridaTemplatePDFMapping = (formData: FormData): { mappings: any; errors: string[] } => {
  const errors: string[] = [];

  console.log('=== CREATING FLORIDA TEMPLATE PDF FIELD MAPPINGS ===');
  console.log('Raw Form Data Summary:');
  console.log('- Seller:', formData.propertyInfo.sellerName);
  console.log('- Buyer:', formData.propertyInfo.buyerName);
  console.log('- Address:', formData.propertyInfo.streetAddress);
  console.log('- County:', formData.propertyInfo.county);
  console.log('- Tax ID:', formData.propertyInfo.propertyTaxId);
  console.log('- Purchase Price:', formData.financialInfo.purchasePrice);

  // Validate required fields first
  const missingFields = validateRequiredFields(formData);
  if (missingFields.length > 0) {
    errors.push(`Missing required fields: ${missingFields.join(', ')}`);
  }

  // Create the mapping using the FloridaTemplate mapping function
  const mappings = mapFormDataToFloridaTemplate(formData);

  // Apply formatting to currency and date fields
  Object.keys(mappings).forEach(fieldName => {
    if (fieldName.includes('Number') && typeof mappings[fieldName] === 'string') {
      const numValue = parseFloat(mappings[fieldName]);
      if (!isNaN(numValue)) {
        mappings[fieldName] = formatCurrencyForPDF(numValue);
      }
    } else if (fieldName.includes('Date') && mappings[fieldName]) {
      mappings[fieldName] = formatDateForPDF(mappings[fieldName]);
    }
  });

  console.log(`✓ MAPPED ${Object.keys(mappings).length} fields for FloridaTemplate.pdf`);
  console.log('=== FLORIDA TEMPLATE MAPPING COMPLETED ===');

  return { mappings, errors };
};

// LEGACY: Main function to create validated PDF field mappings based on placeholder PDF
export const createValidatedPDFMapping = (formData: FormData): { mappings: any; errors: string[] } => {
  const errors: string[] = [];
  const mappings: any = {};

  console.log('=== CREATING PDF FIELD MAPPINGS (PLACEHOLDER-BASED) ===');
  console.log('Raw Form Data Summary:');
  console.log('- Seller:', formData.propertyInfo.sellerName);
  console.log('- Buyer:', formData.propertyInfo.buyerName);
  console.log('- Address:', formData.propertyInfo.streetAddress);
  console.log('- County:', formData.propertyInfo.county);
  console.log('- Tax ID:', formData.propertyInfo.propertyTaxId);
  console.log('- Purchase Price:', formData.financialInfo.purchasePrice);

  // ===== STEP 1: VALIDATE AND PREPARE ALL DATA =====
  console.log('\n🔍 STEP 1: Validating and preparing data...');

  // Validate and prepare names
  const validatedSellerName = validateAndPrepareName(formData.propertyInfo.sellerName, 'Seller');
  const validatedBuyerName = validateAndPrepareName(formData.propertyInfo.buyerName, 'Buyer');

  // Validate and prepare property data
  const validatedAddress = validateAndPrepareAddress(formData.propertyInfo.streetAddress);
  const validatedCounty = validateAndPrepareCounty(formData.propertyInfo.county);
  const validatedTaxId = validateAndPrepareTaxId(formData.propertyInfo.propertyTaxId);

  // Validate and prepare financial data
  const validatedPurchasePrice = validateAndPrepareCurrency(formData.financialInfo.purchasePrice, 'Purchase Price');
  const validatedInitialDeposit = validateAndPrepareCurrency(formData.financialInfo.initialDeposit, 'Initial Deposit');
  const validatedFinancingAmount = validateAndPrepareCurrency(formData.paymentMethod.financingAmount, 'Financing Amount');
  const validatedBalanceToClose = validateAndPrepareCurrency(formData.financialInfo.balanceToClose, 'Balance to Close');

  // Validate and prepare initials
  const validatedBuyerInitials = validateAndPrepareInitials(formData.partyDetails.buyerInitials, 'Buyer');
  const validatedSellerInitials = validateAndPrepareInitials(formData.partyDetails.sellerInitials, 'Seller');

  // Validate and prepare dates
  const validatedContractDate = validateAndPrepareDate(formData.partyDetails.contractDate, 'Contract Date');
  const validatedClosingDate = validateAndPrepareDate(formData.financialInfo.closingDate, 'Closing Date');

  console.log('\n✅ STEP 1 COMPLETE: All data validated and prepared');
  console.log('\n🔍 STEP 2: Creating field mappings with validated data...');
  
  // Helper function to add field with validation
  const addField = (pdfFieldName: string, value: any, validator: (val: any) => ValidationResult) => {
    const validation = validator(value);
    if (!validation.isValid) {
      errors.push(`${pdfFieldName}: ${validation.errors.join(', ')}`);
    }
    mappings[pdfFieldName] = validation.sanitizedValue;
    console.log(`✓ MAPPED: ${pdfFieldName} = "${validation.sanitizedValue}"`);
  };

  // Helper function to try multiple field name variations for the same value
  const addFieldWithVariations = (fieldNames: string[], value: any, validator: (val: any) => ValidationResult) => {
    console.log(`🔍 Trying ${fieldNames.length} field variations for value: "${value}"`);
    fieldNames.forEach(fieldName => {
      addField(fieldName, value, validator);
    });
  };
  
  // ===== SECTION 1: PARTIES (Header Section) =====
  // CONFIRMED PDF STRUCTURE from HTML analysis:
  // Line 1: PARTIES: _________________ ("Seller"),
  // Line 2: and _________________ ("Buyer"),
  //
  // SOLUTION: Map seller name to first field, buyer name to second field

  // ✅ Use VALIDATED names to prevent data corruption
  if (!validatedSellerName || !validatedBuyerName) {
    console.log('❌ CRITICAL ERROR: Seller or Buyer name is invalid after validation');
    console.log(`   Seller: "${validatedSellerName}"`);
    console.log(`   Buyer: "${validatedBuyerName}"`);
    errors.push('Invalid seller or buyer name - cannot create PARTIES field');
  }

  console.log(`🎯 SELLER NAME (Line 1: "PARTIES: _____ ("Seller"),"): "${validatedSellerName}"`);
  console.log(`🎯 BUYER NAME (Line 2: "and _____ ("Buyer"),"): "${validatedBuyerName}"`);

  // SELLER NAME FIELD (Line 1: after "PARTIES:")
  // Based on PDF structure analysis, this is likely the main "PARTIES" field
  addFieldWithVariations([
    'PARTIES',        // Most likely - the main parties field for seller
    'Parties',
    'parties',
    'undefined',      // Fallback options
    'undefined_1',
    'Text1',
    'Text2',
    'Seller',
    'seller'
  ], validatedSellerName, validateText);

  // BUYER NAME FIELD (Line 2: after "and")
  // This field comes after "and" in the PDF structure
  addFieldWithVariations([
    'and',           // Most likely - field that follows "and" text
    'undefined_2',   // Common fallback for second field
    'Buyer',
    'buyer',
    'BUYER',
    'Text3',
    'Text4',
    'and_buyer',
    'parties_buyer'
  ], validatedBuyerName, validateText);
  
  // ===== SECTION 2: PROPERTY DESCRIPTION =====
  // Using VALIDATED data to prevent field contamination

  // ✅ Street Address - Line (a): VALIDATED address only
  if (!validatedAddress) {
    console.log('❌ CRITICAL ERROR: Address is invalid after validation');
    errors.push('Invalid street address');
  }
  console.log(`🎯 Address field: "${validatedAddress}"`);
  addFieldWithVariations([
    'a Street address city zip',
    'Street address city zip',
    'Property Address',
    'Address',
    'Text3',
    'Text4'
  ], validatedAddress, validateAddress);

  // ✅ County field: VALIDATED county (already includes "Florida")
  if (!validatedCounty) {
    console.log('❌ CRITICAL ERROR: County is invalid after validation');
    errors.push('Invalid county name');
  }
  console.log(`🎯 County field: "${validatedCounty}"`);
  addFieldWithVariations([
    'County Florida Property Tax ID',
    'County',
    'County Florida',
    'Text5',
    'Text6'
  ], validatedCounty, validateText);

  // ✅ Tax ID - Line (b): VALIDATED tax ID only
  if (!validatedTaxId) {
    console.log('❌ CRITICAL ERROR: Tax ID is invalid after validation');
    errors.push('Invalid property tax ID format');
  }
  console.log(`🎯 Tax ID field: "${validatedTaxId}"`);
  addFieldWithVariations([
    'b Located in',
    'Property Tax ID',
    'Tax ID',
    'Located in',
    'Text7',
    'Text8'
  ], validatedTaxId, validateTaxId);

  // ✅ Legal Description fields (from field-mapping-report.md)
  addField('c Real Property The legal description is 1', formData.propertyInfo.legalDescription1 || '', (val) => validateText(val, 500));
  addField('c Real Property The legal description is 2', formData.propertyInfo.legalDescription2 || '', (val) => validateText(val, 500));
  
  // ===== SECTION 3: PERSONAL PROPERTY =====
  // Based on field-mapping-report.md specifications

  // ✅ Standard personal property (ranges, ovens, etc.)
  addField('and other access devices and storm shutterspanels Personal Property',
    formData.propertyInfo.personalPropertyIncluded || '',
    (val) => validateText(val, 500));

  // ✅ Additional personal property
  addField('Other Personal Property items included in this purchase are',
    formData.propertyInfo.additionalPersonalProperty || '',
    (val) => validateText(val, 500));

  // ✅ Excluded items
  addField('e The following items are excluded from the purchase',
    formData.propertyInfo.itemsExcluded || '',
    (val) => validateText(val, 500));
    
  // Excluded items
  addField('e The following items are excluded from the purchase', 
    formData.propertyInfo.itemsExcluded, 
    (val) => validateText(val, 500));
  
  // ===== SECTION 4: PURCHASE PRICE AND PAYMENT =====
  // Using VALIDATED and pre-formatted currency data to prevent contamination

  console.log(`🎯 Purchase Price (Text79): "${validatedPurchasePrice}"`);
  console.log(`🎯 Initial Deposit (Text80): "${validatedInitialDeposit}"`);
  console.log(`🎯 Financing Amount (Text82): "${validatedFinancingAmount}"`);
  console.log(`🎯 Balance to Close (Text84): "${validatedBalanceToClose}"`);

  // Purchase Price - Text79 (CRITICAL FIELD) - Use pre-formatted currency
  addField('Text79', validatedPurchasePrice, (val) => ({ isValid: true, errors: [], sanitizedValue: val }));

  // Initial Deposit - Text80 (CRITICAL FIELD) - Use pre-formatted currency
  addField('Text80', validatedInitialDeposit, (val) => ({ isValid: true, errors: [], sanitizedValue: val }));

  // Additional Deposit (if any)
  addField('Text81', '', (val) => ({ isValid: true, errors: [], sanitizedValue: '' }));

  // Financing Amount - Text82 (CRITICAL FIELD) - Use pre-formatted currency
  addField('Text82', validatedFinancingAmount, (val) => ({ isValid: true, errors: [], sanitizedValue: val }));

  // Other Amount
  addField('Text83', '', (val) => ({ isValid: true, errors: [], sanitizedValue: '' }));

  // Balance to Close - Text84 (CRITICAL FIELD) - Use pre-formatted currency
  addField('Text84', validatedBalanceToClose, (val) => ({ isValid: true, errors: [], sanitizedValue: val }));
  
  // ===== SECTION 5: FINANCING =====
  
  // Loan amount field (separate from Text82) - Use pre-formatted currency
  addField('c Financing Express as a dollar amount or percentage Loan Amount see Paragraph 8',
    validatedFinancingAmount,
    (val) => ({ isValid: true, errors: [], sanitizedValue: val }));
  
  // ===== SECTION 6: CLOSING =====

  // Closing Date - using VALIDATED date
  console.log(`🎯 Closing Date: "${validatedClosingDate}"`);
  addField('Closing Date at the time established by the Closing Agent', validatedClosingDate, (val) => ({ isValid: true, errors: [], sanitizedValue: val }));
  
  // ===== SECTION 7: ESCROW AGENT =====

  addField('Escrow Agent Information Name', formData.escrowInfo.escrowAgentName, (val) => validateText(val, 100));
  // ❌ REMOVED: Don't map escrow address to generic "Address" field - it conflicts with property address
  // addField('Address', formData.escrowInfo.escrowAgentAddress, validateAddress);
  addField('Email', '', (val) => ({ isValid: true, errors: [], sanitizedValue: '' }));
  addField('Fax', '', (val) => ({ isValid: true, errors: [], sanitizedValue: '' }));
  
  // ===== SECTION 8: CONTRACT DATES =====

  // All contract date fields - using VALIDATED date
  console.log(`🎯 Contract Date: "${validatedContractDate}"`);
  addField('Date', validatedContractDate, (val) => ({ isValid: true, errors: [], sanitizedValue: val }));
  addField('Date_2', validatedContractDate, (val) => ({ isValid: true, errors: [], sanitizedValue: val }));
  addField('Date_3', validatedContractDate, (val) => ({ isValid: true, errors: [], sanitizedValue: val }));
  addField('Date_4', validatedContractDate, (val) => ({ isValid: true, errors: [], sanitizedValue: val }));
  
  // ===== SECTION 9: BUYER INITIALS (ALL PAGES) =====
  // Using VALIDATED initials to prevent contamination

  if (!validatedBuyerInitials) {
    console.log('❌ CRITICAL ERROR: Buyer initials are invalid after validation');
    errors.push('Invalid buyer initials');
  }
  console.log(`🎯 Buyer Initials: "${validatedBuyerInitials}"`);

  // Primary buyer initials - try multiple field name variations
  addFieldWithVariations([
    'Buyers Initials',
    'Buyers Initials_2',
    'Buyer Initials',
    'Buyer\'s Initials',
    'Initials',
    'Text20',
    'Text21'
  ], validatedBuyerInitials, validateInitials);

  // All undefined fields are buyer initials (based on placeholder analysis)
  const buyerInitialsFields = [
    'undefined_3', 'undefined_4', 'undefined_5', 'undefined_6', 'undefined_7',
    'undefined_8', 'undefined_9', 'undefined_10', 'undefined_11', 'undefined_12',
    'undefined_13', 'undefined_14', 'undefined_15'
  ];

  buyerInitialsFields.forEach(fieldName => {
    addField(fieldName, validatedBuyerInitials, validateInitials);
  });
  
  // ===== SECTION 10: SELLER INITIALS (ALL PAGES) =====
  // Using VALIDATED initials to prevent contamination

  if (!validatedSellerInitials) {
    console.log('❌ CRITICAL ERROR: Seller initials are invalid after validation');
    errors.push('Invalid seller initials');
  }
  console.log(`🎯 Seller Initials: "${validatedSellerInitials}"`);
  
  // All seller initial fields
  const sellerInitialsFields = [
    'Sellers Initials', 'Sellers Initials_2', 'Sellers Initials_3', 'Sellers Initials_4',
    'Sellers Initials_5', 'Sellers Initials_6', 'Sellers Initials_7', 'Sellers Initials_8',
    'Sellers Initials_9', 'Sellers Initials_10', 'Sellers Initials_11'
  ];
  
  sellerInitialsFields.forEach(fieldName => {
    addField(fieldName, validatedSellerInitials, validateInitials);
  });

  // ===== SECTION 11: ADDRESS FOR NOTICES =====
  // Using VALIDATED data for address notices

  console.log(`🎯 Buyer Notice Address: "${validatedBuyerName}" / "${validatedAddress}"`);
  console.log(`🎯 Seller Notice Address: "${validatedSellerName}" / "${validatedAddress}"`);

  // ✅ Buyer's address for notices (using validated data)
  addField('Buyers address for purposes of notice 1', validatedBuyerName, validateName);
  addField('Buyers address for purposes of notice 2', validatedAddress, validateAddress);
  addField('Buyers address for purposes of notice 3', '', (val) => validateText(val, 100)); // Optional

  // ✅ Seller's address for notices (using validated data)
  addField('Sellers address for purposes of notice 1', validatedSellerName, validateName);
  addField('Sellers address for purposes of notice 2', validatedAddress, validateAddress);
  addField('Sellers address for purposes of notice 3', '', (val) => validateText(val, 100)); // Optional
  
  // ===== SECTION 11: PAYMENT METHOD CHECKBOXES =====

  console.log(`🎯 Payment Type: "${formData.paymentMethod.paymentType}"`);
  console.log(`🎯 Loan Type: "${formData.paymentMethod.loanType}"`);
  console.log(`🎯 Interest Rate Type: "${formData.paymentMethod.interestRateType}"`);

  // Cash vs Financing
  const isCash = formData.paymentMethod.paymentType === 'cash';
  const isFinancing = formData.paymentMethod.paymentType === 'financing';

  console.log(`🎯 Is Cash: ${isCash}, Is Financing: ${isFinancing}`);

  mappings['a Buyer will pay cash for the purchase of the Property at Closing There is no financing contingency to Buyers'] = isCash;
  mappings['b This Contract is contingent upon Buyer obtaining approval of a'] = isFinancing;
  
  // Additional checkbox fields for payment method
  mappings['Check Box97'] = formData.paymentMethod.paymentType === 'cash';
  mappings['Check Box99'] = formData.paymentMethod.paymentType === 'financing';
  
  // ===== SECTION 12: LOAN TYPE CHECKBOXES =====

  console.log(`🎯 Checking loan type: "${formData.paymentMethod.loanType}"`);
  const loanType = formData.paymentMethod.loanType?.toLowerCase();

  mappings['conventional'] = loanType === 'conventional';
  mappings['FHA'] = loanType === 'fha';
  mappings['VA or'] = loanType === 'va';
  mappings['other'] = loanType === 'other';

  console.log(`🎯 Conventional checkbox: ${mappings['conventional']}`);

  // ===== SECTION 13: INTEREST RATE TYPE CHECKBOXES =====

  console.log(`🎯 Checking interest rate type: "${formData.paymentMethod.interestRateType}"`);
  const interestRateType = formData.paymentMethod.interestRateType?.toLowerCase();

  mappings['fixed'] = interestRateType === 'fixed';
  mappings['adjustable'] = interestRateType === 'adjustable';

  console.log(`🎯 Fixed rate checkbox: ${mappings['fixed']}`);
  
  // ===== SECTION 14: CLOSING COST RESPONSIBILITY =====
  
  mappings['i Seller shall designate Closing Agent and pay for Owners Policy and Charges and Buyer shall pay the'] = 
    formData.titleClosingLogic.closingOption === 'seller-designates';
  mappings['ii Buyer shall designate Closing Agent and pay for Owners Policy and Charges and charges for closing'] = 
    formData.titleClosingLogic.closingOption === 'buyer-designates';
  
  // Additional checkbox fields
  mappings['Check Box101'] = formData.titleClosingLogic.closingOption === 'seller-designates';
  mappings['Check Box102'] = formData.titleClosingLogic.closingOption === 'buyer-designates';
  
  // ===== SECTION 15: DEPOSIT TIMING =====

  // Deposit accompanies offer checkbox
  mappings['accompanies offer or ii'] = formData.financialInfo.initialDeposit > 0;
  mappings['Check Box98'] = formData.financialInfo.initialDeposit > 0; // Alternative checkbox name

  // 3-day deposit checkbox
  mappings['blank then 3 days after Effective Date IF NEITHER BOX IS CHECKED THEN'] = formData.financialInfo.initialDeposit === 0;
  mappings['Check Box100'] = formData.financialInfo.initialDeposit === 0; // Alternative checkbox name
  
  // ===== SECTION 16: ADDITIONAL TERMS (1-16) =====
  
  const additionalTermsFields = [
    '20 ADDITIONAL TERMS 1', '20 ADDITIONAL TERMS 2', '20 ADDITIONAL TERMS 3', 
    '20 ADDITIONAL TERMS 4', '20 ADDITIONAL TERMS 5', '20 ADDITIONAL TERMS 6',
    '20 ADDITIONAL TERMS 7', '20 ADDITIONAL TERMS 8', '20 ADDITIONAL TERMS 9',
    '20 ADDITIONAL TERMS 10', '20 ADDITIONAL TERMS 11', '20 ADDITIONAL TERMS 12',
    '20 ADDITIONAL TERMS 13', '20 ADDITIONAL TERMS 14', '20 ADDITIONAL TERMS 15',
    '20 ADDITIONAL TERMS 16'
  ];
  
  additionalTermsFields.forEach((fieldName, index) => {
    const termValue = formData.additionalTerms[index] || '';
    addField(fieldName, termValue, (val) => validateText(val, 200));
  });
  
  // ===== SECTION 17: ADDRESS FOR NOTICES =====
  
  // Buyer address for notices
  addField('Buyers address for purposes of notice 1', formData.propertyInfo.buyerName.toUpperCase(), validateName);
  addField('Buyers address for purposes of notice 2', formData.propertyInfo.streetAddress, validateAddress);
  addField('Buyers address for purposes of notice 3', '', (val) => ({ isValid: true, errors: [], sanitizedValue: '' }));
  
  // Seller address for notices
  addField('Sellers address for purposes of notice 1', formData.propertyInfo.sellerName.toUpperCase(), validateName);
  addField('Sellers address for purposes of notice 2', formData.propertyInfo.streetAddress, validateAddress);
  addField('Sellers address for purposes of notice 3', '', (val) => ({ isValid: true, errors: [], sanitizedValue: '' }));
  
  // ===== SECTION 18: CONTRACT RIDERS (FUTURE FEATURES) =====
  
  mappings['A Condominium Rider'] = false;
  mappings['B Homeowners Assn'] = false;
  mappings['C Seller Financing'] = false;
  mappings['F Appraisal Contingency'] = false;
  mappings['G Short Sale'] = false;
  
  // ===== SECTION 19: REAL ESTATE PROFESSIONALS =====
  
  addField('Cooperating Sales Associate if any', '', (val) => ({ isValid: true, errors: [], sanitizedValue: '' }));
  addField('Listing Sales Associate', '', (val) => ({ isValid: true, errors: [], sanitizedValue: '' }));
  addField('Cooperating Broker if any', '', (val) => ({ isValid: true, errors: [], sanitizedValue: '' }));
  addField('Listing Broker', '', (val) => ({ isValid: true, errors: [], sanitizedValue: '' }));

  // ===== SECTION 20: CONTINGENCY PERIODS =====

  // Note: These fields may not exist in current PDF but are common in real estate contracts
  // They will be mapped to available text fields if the PDF supports them

  // Inspection period
  const inspectionPeriod = formData.contingencies?.inspectionPeriod || '';
  if (inspectionPeriod) {
    addFieldWithVariations([
      'Inspection Period',
      'inspection period',
      'days for inspection',
      'Text90',
      'Text91'
    ], `${inspectionPeriod} days`, validateText);
  }

  // Financing deadline
  const financingDeadline = formData.contingencies?.financingDeadline || '';
  if (financingDeadline) {
    addFieldWithVariations([
      'Financing Deadline',
      'financing deadline',
      'days for financing',
      'Text92',
      'Text93'
    ], `${financingDeadline} days`, validateText);
  }

  // Appraisal contingency
  const appraisalContingency = formData.contingencies?.appraisalContingency || '';
  if (appraisalContingency) {
    addFieldWithVariations([
      'Appraisal Contingency',
      'appraisal contingency',
      'days for appraisal',
      'Text94',
      'Text95'
    ], `${appraisalContingency} days`, validateText);
  }

  // ===== SECTION 21: CLEAR UNUSED FIELDS =====
  
  const unusedTextFields = ['Text85', 'Text86', 'Text87', 'Text88', 'Text89', 'Text90', 'Text91', 'Text92', 'Text93', 'Text94', 'Text95', 'Text96', 'Text103'];
  unusedTextFields.forEach(fieldName => {
    mappings[fieldName] = '';
  });
  
  // Clear undefined_2 (reserved field)
  addField('undefined_2', '', (val) => ({ isValid: true, errors: [], sanitizedValue: '' }));
  
  // Clear financing timing field
  addField('if left blank then 5 days', '', (val) => ({ isValid: true, errors: [], sanitizedValue: '' }));
  
  // ===== FINAL VERIFICATION BASED ON UPDATED FIELD MAPPING =====
  console.log('=== FIELD MAPPING VERIFICATION (SEPARATE SELLER/BUYER) ===');
  console.log('✓ SELLER NAME (Line 1):', mappings['undefined'] || mappings['PARTIES'] || 'NOT MAPPED');
  console.log('✓ BUYER NAME (Line 2):', mappings['and'] || mappings['undefined_2'] || 'NOT MAPPED');
  console.log('✓ Property Address (line a):', mappings['a Street address city zip']);
  console.log('✓ County field:', mappings['County Florida Property Tax ID']);
  console.log('✓ Tax ID (line b):', mappings['b Located in']);
  console.log('✓ Purchase Price (Text79):', mappings['Text79']);
  console.log('✓ Initial Deposit (Text80):', mappings['Text80']);
  console.log('✓ Financing Amount (Text82):', mappings['Text82']);
  console.log('✓ Balance to Close (Text84):', mappings['Text84']);
  console.log('✓ Buyer Initials:', mappings['Buyers Initials']);
  console.log('✓ Seller Initials:', mappings['Sellers Initials']);
  console.log('✓ Closing Date:', mappings['Closing Date at the time established by the Closing Agent']);
  console.log('✓ Contract Date:', mappings['Date']);
  console.log('✓ Escrow Agent:', mappings['Escrow Agent Information Name']);
  console.log('✓ Total Fields Mapped:', Object.keys(mappings).length);
  console.log('✓ Validation Errors:', errors.length);

  // Log all field mappings for debugging
  console.log('=== ALL FIELD MAPPINGS ===');
  Object.entries(mappings).forEach(([key, value]) => {
    if (typeof value !== 'boolean' && value) {
      console.log(`"${key}": "${value}"`);
    }
  });
  console.log('=== END PLACEHOLDER-BASED VERIFICATION ===');

  // ===== FINAL VALIDATION SUMMARY =====
  console.log('\n🔍 FINAL VALIDATION SUMMARY:');
  console.log('================================');
  console.log(`✅ Seller Name (Line 1): "${validatedSellerName}"`);
  console.log(`✅ Buyer Name (Line 2): "${validatedBuyerName}"`);
  console.log(`✅ SEPARATE FIELDS (NOT combined): Seller and Buyer mapped to different PDF fields`);
  console.log(`✅ Address: "${validatedAddress}"`);
  console.log(`✅ County: "${validatedCounty}"`);
  console.log(`✅ Tax ID: "${validatedTaxId}"`);
  console.log(`✅ Purchase Price: "${validatedPurchasePrice}"`);
  console.log(`✅ Initial Deposit: "${validatedInitialDeposit}"`);
  console.log(`✅ Financing Amount: "${validatedFinancingAmount}"`);
  console.log(`✅ Balance to Close: "${validatedBalanceToClose}"`);
  console.log(`✅ Buyer Initials: "${validatedBuyerInitials}"`);
  console.log(`✅ Seller Initials: "${validatedSellerInitials}"`);
  console.log(`✅ Contract Date: "${validatedContractDate}"`);
  console.log(`✅ Closing Date: "${validatedClosingDate}"`);
  console.log(`✅ Payment Type: "${formData.paymentMethod.paymentType}" (Cash: ${formData.paymentMethod.paymentType === 'cash'})`);
  console.log('================================');

  if (errors.length > 0) {
    console.log('❌ VALIDATION ERRORS FOUND:');
    errors.forEach(error => console.log(`   - ${error}`));
  } else {
    console.log('✅ ALL DATA VALIDATED SUCCESSFULLY - NO ERRORS');
  }

  console.log(`📊 Total fields mapped: ${Object.keys(mappings).length}`);
  console.log('=== PDF FIELD MAPPING COMPLETE ===\n');

  return { mappings, errors };
};

// Enhanced validation functions for FloridaTemplate.pdf fields
export const validateFloridaTemplatePDFFields = (formData: FormData): ValidationResult[] => {
  const results: ValidationResult[] = [];

  // Validate seller name (maps to '(Text_1)')
  if (formData.propertyInfo.sellerName) {
    results.push(validateName(formData.propertyInfo.sellerName, 'Seller Name'));
  }

  // Validate buyer name (maps to '(Text_2)')
  if (formData.propertyInfo.buyerName) {
    results.push(validateName(formData.propertyInfo.buyerName, 'Buyer Name'));
  }

  // Validate property address (maps to '(Text_3)')
  if (formData.propertyInfo.streetAddress) {
    results.push(validateAddress(formData.propertyInfo.streetAddress, 'Property Address'));
  }

  // Validate county (maps to '(Text_4)')
  if (formData.propertyInfo.county) {
    results.push(validateText(formData.propertyInfo.county, 'County', 50));
  }

  // Validate property tax ID (maps to '(Text_5)')
  if (formData.propertyInfo.propertyTaxId) {
    results.push(validateTaxId(formData.propertyInfo.propertyTaxId, 'Property Tax ID'));
  }

  // Validate legal description lines (maps to '(Text_6)' and '(Text_7)')
  if (formData.propertyInfo.legalDescription1) {
    results.push(validateLegalDescription(formData.propertyInfo.legalDescription1, 'Legal Description Line 1'));
  }
  if (formData.propertyInfo.legalDescription2) {
    results.push(validateLegalDescription(formData.propertyInfo.legalDescription2, 'Legal Description Line 2'));
  }

  // Validate purchase price (maps to '(Number_1)')
  if (formData.financialInfo.purchasePrice) {
    results.push(validateCurrency(formData.financialInfo.purchasePrice, 'Purchase Price'));
  }

  // Validate initial deposit (maps to '(Number_2)')
  if (formData.financialInfo.initialDeposit) {
    results.push(validateCurrency(formData.financialInfo.initialDeposit, 'Initial Deposit'));
  }

  // Validate balance to close (maps to '(Number_3)')
  if (formData.financialInfo.balanceToClose) {
    results.push(validateCurrency(formData.financialInfo.balanceToClose, 'Balance to Close'));
  }

  // Validate contract date (maps to '(Date_1)')
  if (formData.partyDetails.contractDate) {
    results.push(validateDate(formData.partyDetails.contractDate, 'Contract Date'));
  }

  // Validate seller initials (maps to multiple '(Initials_X)' fields)
  if (formData.partyDetails.sellerInitials) {
    results.push(validateInitials(formData.partyDetails.sellerInitials, 'Seller Initials'));
  }

  // Validate buyer initials (maps to multiple '(Initials_X)' fields)
  if (formData.partyDetails.buyerInitials) {
    results.push(validateInitials(formData.partyDetails.buyerInitials, 'Buyer Initials'));
  }

  // Validate personal property included (maps to '(Text_9)')
  if (formData.propertyInfo.personalPropertyIncluded) {
    results.push(validateText(formData.propertyInfo.personalPropertyIncluded, 'Personal Property Included', 500));
  }

  // Validate items excluded (maps to '(Text_11)')
  if (formData.propertyInfo.itemsExcluded) {
    results.push(validateText(formData.propertyInfo.itemsExcluded, 'Items Excluded', 500));
  }

  // Validate payment method selection (maps to checkboxes)
  if (formData.paymentMethod.paymentType) {
    const validPaymentTypes = ['cash', 'financing'];
    if (!validPaymentTypes.includes(formData.paymentMethod.paymentType)) {
      results.push({
        isValid: false,
        field: 'Payment Type',
        message: 'Payment type must be either "cash" or "financing"',
        errors: ['Invalid payment type'],
        sanitizedValue: ''
      });
    }
  }

  // Validate loan type if financing is selected
  if (formData.paymentMethod.paymentType === 'financing' && formData.paymentMethod.loanType) {
    const validLoanTypes = ['conventional', 'fha', 'va', 'other'];
    if (!validLoanTypes.includes(formData.paymentMethod.loanType)) {
      results.push({
        isValid: false,
        field: 'Loan Type',
        message: 'Loan type must be one of: conventional, fha, va, other',
        errors: ['Invalid loan type'],
        sanitizedValue: ''
      });
    }
  }

  return results;
};

// Legacy validation function (kept for backward compatibility)
export const validatePDFFields = (formData: FormData): ValidationResult[] => {
  return validateFloridaTemplatePDFFields(formData);
};

// Export validation functions for use in components
export {
  validateTaxId,
  validateAddress,
  validateCurrency,
  validateDate,
  validateText,
  validateName,
  validateInitials,
  validateLegalDescription
};