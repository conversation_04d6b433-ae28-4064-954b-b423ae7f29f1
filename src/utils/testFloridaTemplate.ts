import { FormData } from '../types/FormTypes';
import { mapFormDataToFloridaTemplate, validateRequiredFields } from './floridaTemplateMappings';
import { validateFloridaTemplatePDFFields } from './pdfFieldMappings';

/**
 * Test data for FloridaTemplate.pdf mapping
 */
export const createTestFormData = (): FormData => {
  return {
    propertyInfo: {
      sellerName: 'SARAH JOHNSON',
      buyerName: 'MICHAEL RODRIGUEZ',
      streetAddress: '123 Ocean Drive, Miami Beach, FL 33139',
      county: 'Miami-Dade',
      propertyTaxId: '12-3456-789-0123',
      legalDescription1: 'Lot 15, Block 3, SUNSET ESTATES SUBDIVISION',
      legalDescription2: 'According to the plat thereof recorded in Plat Book 45, Page 67',
      personalPropertyIncluded: 'All kitchen appliances including refrigerator, washer, dryer, microwave, dishwasher, and security system',
      additionalPersonalProperty: 'Window treatments and ceiling fans',
      itemsExcluded: 'Outdoor furniture and artwork'
    },
    financialInfo: {
      purchasePrice: 750000,
      initialDeposit: 75000,
      balanceToClose: 675000,
      closingDate: '2025-08-15'
    },
    paymentMethod: {
      paymentType: 'financing' as const,
      loanType: 'conventional' as const,
      financingAmount: 600000,
      interestRateType: 'fixed' as const
    },
    partyDetails: {
      contractDate: '2025-07-15',
      sellerInitials: 'SJ',
      buyerInitials: 'MR'
    },
    escrowInfo: {
      escrowAgentName: 'Sunshine Title Company',
      escrowAgentAddress: '456 Title Street, Miami, FL 33101'
    },
    titleClosingLogic: {
      closingOption: 'seller-designates' as const
    },
    additionalTerms: [
      'Property sold AS-IS with right to inspect',
      'Seller to provide termite inspection',
      'Buyer to verify all permits and certificates'
    ],
    contingencies: {
      inspectionPeriod: '10',
      financingDeadline: '30',
      appraisalContingency: '15'
    }
  };
};

/**
 * Test the FloridaTemplate mapping function
 */
export const testFloridaTemplateMapping = (): void => {
  console.log('=== TESTING FLORIDA TEMPLATE MAPPING ===');
  
  const testData = createTestFormData();
  
  // Test 1: Basic mapping
  console.log('\n1. Testing basic field mapping...');
  const mappedFields = mapFormDataToFloridaTemplate(testData);
  
  console.log(`✓ Mapped ${Object.keys(mappedFields).length} fields`);
  
  // Test 2: Verify key fields are mapped correctly
  console.log('\n2. Verifying key field mappings...');
  const keyFieldTests = [
    { field: "'(Text_1)'", expected: 'SARAH JOHNSON', description: 'Seller Name' },
    { field: "'(Text_2)'", expected: 'MICHAEL RODRIGUEZ', description: 'Buyer Name' },
    { field: "'(Text_3)'", expected: '123 Ocean Drive, Miami Beach, FL 33139', description: 'Property Address' },
    { field: "'(Text_4)'", expected: 'Miami-Dade', description: 'County' },
    { field: "'(Text_5)'", expected: '12-3456-789-0123', description: 'Property Tax ID' },
    { field: "'(Number_1)'", expected: '750000', description: 'Purchase Price' },
    { field: "'(Number_2)'", expected: '75000', description: 'Initial Deposit' },
    { field: "'(Checkbox_9)'", expected: true, description: 'Conventional Loan Checkbox' },
    { field: "'(Date_1)'", expected: '2025-07-15', description: 'Contract Date' },
    { field: "'(Initials_1)'", expected: 'SJ', description: 'Seller Initials' },
    { field: "'(Initials_2)'", expected: 'MR', description: 'Buyer Initials' }
  ];
  
  let passedTests = 0;
  keyFieldTests.forEach(test => {
    const actualValue = mappedFields[test.field];
    const passed = actualValue === test.expected;
    
    if (passed) {
      console.log(`✓ ${test.description}: ${test.field} = "${actualValue}"`);
      passedTests++;
    } else {
      console.log(`✗ ${test.description}: ${test.field} = "${actualValue}" (expected: "${test.expected}")`);
    }
  });
  
  console.log(`\n✓ Passed ${passedTests}/${keyFieldTests.length} key field tests`);
  
  // Test 3: Validate required fields
  console.log('\n3. Testing required field validation...');
  const missingFields = validateRequiredFields(testData);
  
  if (missingFields.length === 0) {
    console.log('✓ All required fields are present');
  } else {
    console.log(`✗ Missing required fields: ${missingFields.join(', ')}`);
  }
  
  // Test 4: Test field validation
  console.log('\n4. Testing field validation...');
  const validationResults = validateFloridaTemplatePDFFields(testData);
  const validationErrors = validationResults.filter(result => !result.isValid);
  
  if (validationErrors.length === 0) {
    console.log('✓ All fields passed validation');
  } else {
    console.log(`✗ Validation errors found:`);
    validationErrors.forEach(error => {
      console.log(`   - ${error.field}: ${error.message}`);
    });
  }
  
  // Test 5: Test checkbox mappings
  console.log('\n5. Testing checkbox mappings...');
  const checkboxTests = [
    { field: "'(Checkbox_8)'", expected: false, description: 'Cash Payment (should be false for financing)' },
    { field: "'(Checkbox_9)'", expected: true, description: 'Conventional Loan (should be true)' },
    { field: "'(Checkbox_10)'", expected: false, description: 'FHA Loan (should be false)' },
    { field: "'(Checkbox_11)'", expected: false, description: 'VA Loan (should be false)' }
  ];
  
  let passedCheckboxTests = 0;
  checkboxTests.forEach(test => {
    const actualValue = mappedFields[test.field];
    const passed = actualValue === test.expected;
    
    if (passed) {
      console.log(`✓ ${test.description}: ${test.field} = ${actualValue}`);
      passedCheckboxTests++;
    } else {
      console.log(`✗ ${test.description}: ${test.field} = ${actualValue} (expected: ${test.expected})`);
    }
  });
  
  console.log(`\n✓ Passed ${passedCheckboxTests}/${checkboxTests.length} checkbox tests`);
  
  // Test 6: Test initials mapping (verify multiple fields get same initials)
  console.log('\n6. Testing initials field population...');
  const initialsFields = Object.keys(mappedFields).filter(key => key.includes('Initials_'));
  const sellerInitialsFields = initialsFields.filter((_, index) => index % 2 === 0); // Odd indices (0-based) = seller
  const buyerInitialsFields = initialsFields.filter((_, index) => index % 2 === 1); // Even indices (0-based) = buyer
  
  const sellerInitialsCorrect = sellerInitialsFields.every(field => mappedFields[field] === 'SJ');
  const buyerInitialsCorrect = buyerInitialsFields.every(field => mappedFields[field] === 'MR');
  
  console.log(`✓ Seller initials fields: ${sellerInitialsFields.length} (all correct: ${sellerInitialsCorrect})`);
  console.log(`✓ Buyer initials fields: ${buyerInitialsFields.length} (all correct: ${buyerInitialsCorrect})`);
  
  // Summary
  console.log('\n=== TEST SUMMARY ===');
  console.log(`✓ Total fields mapped: ${Object.keys(mappedFields).length}`);
  console.log(`✓ Key field tests: ${passedTests}/${keyFieldTests.length}`);
  console.log(`✓ Checkbox tests: ${passedCheckboxTests}/${checkboxTests.length}`);
  console.log(`✓ Required fields: ${missingFields.length === 0 ? 'PASS' : 'FAIL'}`);
  console.log(`✓ Field validation: ${validationErrors.length === 0 ? 'PASS' : 'FAIL'}`);
  console.log(`✓ Initials mapping: ${sellerInitialsCorrect && buyerInitialsCorrect ? 'PASS' : 'FAIL'}`);
  
  const allTestsPassed = passedTests === keyFieldTests.length && 
                         passedCheckboxTests === checkboxTests.length && 
                         missingFields.length === 0 && 
                         validationErrors.length === 0 && 
                         sellerInitialsCorrect && 
                         buyerInitialsCorrect;
  
  console.log(`\n🎯 OVERALL RESULT: ${allTestsPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  console.log('=== END FLORIDA TEMPLATE MAPPING TEST ===\n');
  
  return;
};

/**
 * Test with edge cases and missing data
 */
export const testFloridaTemplateMappingEdgeCases = (): void => {
  console.log('=== TESTING FLORIDA TEMPLATE MAPPING - EDGE CASES ===');
  
  // Test with minimal data
  const minimalData: FormData = {
    propertyInfo: {
      sellerName: 'John Doe',
      buyerName: 'Jane Smith',
      streetAddress: '123 Main St, City, FL 12345',
      county: 'Orange',
      propertyTaxId: '',
      legalDescription1: '',
      legalDescription2: '',
      personalPropertyIncluded: '',
      additionalPersonalProperty: '',
      itemsExcluded: ''
    },
    financialInfo: {
      purchasePrice: 100000,
      initialDeposit: 5000,
      balanceToClose: 95000,
      closingDate: ''
    },
    paymentMethod: {
      paymentType: 'cash' as const,
      loanType: undefined,
      financingAmount: undefined,
      interestRateType: undefined
    },
    partyDetails: {
      contractDate: '2025-07-15',
      sellerInitials: 'JD',
      buyerInitials: 'JS'
    },
    escrowInfo: {
      escrowAgentName: '',
      escrowAgentAddress: ''
    },
    titleClosingLogic: {
      closingOption: 'buyer-designates' as const
    },
    additionalTerms: [],
    contingencies: undefined
  };
  
  console.log('\n1. Testing with minimal data...');
  const mappedMinimal = mapFormDataToFloridaTemplate(minimalData);
  const missingFieldsMinimal = validateRequiredFields(minimalData);
  
  console.log(`✓ Mapped ${Object.keys(mappedMinimal).length} fields with minimal data`);
  console.log(`✓ Missing required fields: ${missingFieldsMinimal.length}`);
  
  // Test cash payment checkbox
  const cashCheckbox = mappedMinimal["'(Checkbox_8)'"];
  console.log(`✓ Cash payment checkbox: ${cashCheckbox} (should be true for cash payment)`);
  
  console.log('\n=== END EDGE CASES TEST ===\n');
};
