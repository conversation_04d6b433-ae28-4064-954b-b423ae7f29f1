import { FormData } from '../types/FormTypes';

/**
 * Comprehensive mapping between FormData structure and FloridaTemplate.pdf field names
 * This file maps the multiform data to the exact field names in the fillable PDF
 */

export interface FloridaPDFFieldMapping {
  [pdfFieldName: string]: any;
}

/**
 * Maps FormData to FloridaTemplate.pdf field names
 * @param formData - The structured form data from the multiform
 * @returns Object with PDF field names as keys and values to fill
 */
export const mapFormDataToFloridaTemplate = (formData: FormData): FloridaPDFFieldMapping => {
  const mapping: FloridaPDFFieldMapping = {};

  // ===== PROPERTY INFORMATION =====
  // Core property details
  mapping["'(Text_1)'"] = formData.propertyInfo.sellerName || '';
  mapping["'(Text_2)'"] = formData.propertyInfo.buyerName || '';
  mapping["'(Text_3)'"] = formData.propertyInfo.streetAddress || '';
  mapping["'(Text_4)'"] = formData.propertyInfo.county || '';
  mapping["'(Text_5)'"] = formData.propertyInfo.propertyTaxId || '';
  mapping["'(Text_6)'"] = formData.propertyInfo.legalDescription1 || '';
  mapping["'(Text_7)'"] = formData.propertyInfo.legalDescription2 || '';
  mapping["'(Text_9)'"] = formData.propertyInfo.personalPropertyIncluded || '';
  mapping["'(Text_10)'"] = formData.propertyInfo.additionalPersonalProperty || '';
  mapping["'(Text_11)'"] = formData.propertyInfo.itemsExcluded || '';

  // ===== FINANCIAL INFORMATION =====
  // Purchase price and financial details
  mapping["'(Number_1)'"] = formData.financialInfo.purchasePrice?.toString() || '';
  mapping["'(Number_2)'"] = formData.financialInfo.initialDeposit?.toString() || '';
  mapping["'(Number_3)'"] = formData.financialInfo.balanceToClose?.toString() || '';
  
  // ===== PAYMENT METHOD CHECKBOXES =====
  // Payment type selection
  mapping["'(Checkbox_8)'"] = formData.paymentMethod.paymentType === 'cash';
  mapping["'(Checkbox_9)'"] = formData.paymentMethod.loanType === 'conventional';
  mapping["'(Checkbox_10)'"] = formData.paymentMethod.loanType === 'fha';
  mapping["'(Checkbox_11)'"] = formData.paymentMethod.loanType === 'va';

  // ===== INCLUDED ITEMS CHECKBOXES =====
  // Personal property included with sale
  const includedItems = formData.propertyInfo.personalPropertyIncluded?.toLowerCase() || '';
  mapping["'(Checkbox_16)'"] = includedItems.includes('refrigerator');
  mapping["'(Checkbox_17)'"] = includedItems.includes('washer');
  mapping["'(Checkbox_18)'"] = includedItems.includes('dryer');
  mapping["'(Checkbox_19)'"] = includedItems.includes('microwave');
  mapping["'(Checkbox_20)'"] = includedItems.includes('dishwasher');
  mapping["'(Checkbox_21)'"] = includedItems.includes('security');

  // ===== ESCROW INFORMATION =====
  mapping["'(Text_12)'"] = formData.escrowInfo.escrowAgentName || '';
  mapping["'(Text_13)'"] = formData.escrowInfo.escrowAgentAddress || '';

  // ===== DATES =====
  mapping["'(Date_1)'"] = formData.partyDetails.contractDate || '';
  mapping["'(Date_2)'"] = formData.partyDetails.contractDate || ''; // Seller signature date
  mapping["'(Date_3)'"] = formData.partyDetails.contractDate || ''; // Buyer signature date

  // ===== INITIALS =====
  // Populate all initial fields with party initials
  const sellerInitials = formData.partyDetails.sellerInitials || '';
  const buyerInitials = formData.partyDetails.buyerInitials || '';
  
  // Page 1 initials
  mapping["'(Initials_1)'"] = sellerInitials;
  mapping["'(Initials_2)'"] = buyerInitials;
  mapping["'(Initials_3)'"] = sellerInitials;
  mapping["'(Initials_4)'"] = buyerInitials;
  
  // Additional pages initials (auto-populate based on form data)
  for (let i = 5; i <= 36; i++) {
    const isSellerField = i % 2 === 1; // Odd numbers for seller, even for buyer
    mapping[`'(Initials_${i})'`] = isSellerField ? sellerInitials : buyerInitials;
  }

  // ===== ADDITIONAL TERMS =====
  // Map additional terms to available text fields
  if (formData.additionalTerms && formData.additionalTerms.length > 0) {
    formData.additionalTerms.forEach((term, index) => {
      if (index < 10) { // Limit to available fields
        const fieldNumber = 16 + index; // Start from Text_16
        mapping[`'(Text_${fieldNumber})'`] = term;
      }
    });
  }

  // ===== FINANCING DETAILS =====
  if (formData.paymentMethod.financingAmount) {
    mapping["'(Number_4)'"] = formData.paymentMethod.financingAmount.toString();
  }

  // ===== CLOSING DATE =====
  if (formData.financialInfo.closingDate) {
    mapping["'(Date_4)'"] = formData.financialInfo.closingDate;
    mapping["'(Date_5)'"] = formData.financialInfo.closingDate;
  }

  return mapping;
};

/**
 * Get all available PDF field names from FloridaTemplate.pdf
 * This helps with debugging and validation
 */
export const getFloridaTemplateFieldNames = (): string[] => {
  const fields: string[] = [];
  
  // Text fields
  for (let i = 1; i <= 58; i++) {
    fields.push(`'(Text_${i})'`);
  }
  
  // Number fields
  for (let i = 1; i <= 9; i++) {
    fields.push(`'(Number_${i})'`);
  }
  
  // Checkbox fields
  for (let i = 1; i <= 73; i++) {
    fields.push(`'(Checkbox_${i})'`);
  }
  
  // Date fields
  for (let i = 1; i <= 5; i++) {
    fields.push(`'(Date_${i})'`);
  }
  
  // Signature fields
  for (let i = 1; i <= 4; i++) {
    fields.push(`'(Signature_${i})'`);
  }
  
  // Initials fields
  for (let i = 1; i <= 36; i++) {
    fields.push(`'(Initials_${i})'`);
  }
  
  // Special fields
  fields.push("'(US_Phone_Number_1)'");
  fields.push("'(US_Phone_Number_2)'");
  fields.push("'(Email_1)'");
  
  return fields;
};

/**
 * Validate that all required fields are mapped
 * @param formData - The form data to validate
 * @returns Array of missing required fields
 */
export const validateRequiredFields = (formData: FormData): string[] => {
  const missing: string[] = [];
  
  // Required fields for a valid contract
  const requiredFields = [
    { field: 'sellerName', path: 'propertyInfo.sellerName' },
    { field: 'buyerName', path: 'propertyInfo.buyerName' },
    { field: 'streetAddress', path: 'propertyInfo.streetAddress' },
    { field: 'county', path: 'propertyInfo.county' },
    { field: 'purchasePrice', path: 'financialInfo.purchasePrice' },
    { field: 'contractDate', path: 'partyDetails.contractDate' },
  ];
  
  requiredFields.forEach(({ field, path }) => {
    const value = path.split('.').reduce((obj: any, key) => obj?.[key], formData);
    if (!value || (typeof value === 'string' && !value.trim())) {
      missing.push(field);
    }
  });
  
  return missing;
};

/**
 * Format currency values for PDF display
 */
export const formatCurrencyForPDF = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

/**
 * Format date for PDF display (MM/DD/YYYY)
 */
export const formatDateForPDF = (dateString: string): string => {
  if (!dateString) return '';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US');
  } catch {
    return dateString; // Return as-is if parsing fails
  }
};
