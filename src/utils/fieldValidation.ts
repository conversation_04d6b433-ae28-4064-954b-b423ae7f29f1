// Field validation utilities for PDF form data
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  sanitizedValue: any;
}

export interface FieldMapping {
  pdfFieldName: string;
  dataType: 'text' | 'number' | 'currency' | 'date' | 'boolean' | 'taxId' | 'address';
  maxLength?: number;
  required: boolean;
  validator?: (value: any) => ValidationResult;
}

// Tax ID validation (flexible format for property tax IDs)
export const validateTaxId = (value: string): ValidationResult => {
  const sanitized = String(value || '').trim();

  if (!sanitized) {
    return { isValid: false, errors: ['Tax ID is required'], sanitizedValue: '' };
  }

  // More flexible patterns for property tax IDs
  const validPatterns = [
    /^\d{2}-\d{4,7}$/,           // XX-XXXX to XX-XXXXXXX
    /^\d{2}-\d{4}-\d{3}-\d{4}$/, // XX-XXXX-XXX-XXXX
    /^\d{8,15}$/,                // 8-15 digit number
    /^[A-Z0-9\-]{5,20}$/         // Alphanumeric with dashes, 5-20 chars
  ];

  const isValid = validPatterns.some(pattern => pattern.test(sanitized));

  if (!isValid) {
    return {
      isValid: false,
      errors: ['Tax ID format not recognized. Please enter a valid property tax ID'],
      sanitizedValue: sanitized
    };
  }

  return { isValid: true, errors: [], sanitizedValue: sanitized };
};

// Address validation (text only, no numbers that look like currency)
export const validateAddress = (value: string): ValidationResult => {
  const sanitized = String(value || '').trim();
  
  if (!sanitized) {
    return { isValid: false, errors: ['Address is required'], sanitizedValue: '' };
  }
  
  // CRITICAL: Enhanced check for monetary values in address fields
  if (/^\$/.test(sanitized) || /^\d+(\.\d{2})?$/.test(sanitized) || /^\d{1,3}(,\d{3})*(\.\d{2})?$/.test(sanitized)) {
    return { 
      isValid: false, 
      errors: [`Address cannot be a monetary value. Received: "${sanitized}"`], 
      sanitizedValue: sanitized 
    };
  }
  
  if (sanitized.length > 200) {
    return { 
      isValid: false, 
      errors: ['Address cannot exceed 200 characters'], 
      sanitizedValue: sanitized.substring(0, 200) 
    };
  }
  
  return { isValid: true, errors: [], sanitizedValue: sanitized };
};

// Currency validation
export const validateCurrency = (value: number | string): ValidationResult => {
  const numValue = typeof value === 'string' ? parseFloat(value.replace(/[^0-9.-]/g, '')) : value;
  
  if (isNaN(numValue) || numValue < 0) {
    return { 
      isValid: false, 
      errors: ['Must be a valid positive number'], 
      sanitizedValue: 0 
    };
  }
  
  return { isValid: true, errors: [], sanitizedValue: numValue };
};

// Date validation
export const validateDate = (value: string): ValidationResult => {
  const sanitized = String(value || '').trim();
  
  if (!sanitized) {
    return { isValid: false, errors: ['Date is required'], sanitizedValue: '' };
  }
  
  const date = new Date(sanitized);
  if (isNaN(date.getTime())) {
    return { 
      isValid: false, 
      errors: ['Must be a valid date'], 
      sanitizedValue: sanitized 
    };
  }
  
  return { isValid: true, errors: [], sanitizedValue: sanitized };
};

// Text validation with length limits
export const validateText = (value: string, maxLength: number = 500): ValidationResult => {
  const sanitized = String(value || '').trim();
  
  if (sanitized.length > maxLength) {
    return { 
      isValid: false, 
      errors: [`Text cannot exceed ${maxLength} characters`], 
      sanitizedValue: sanitized.substring(0, maxLength) 
    };
  }
  
  return { isValid: true, errors: [], sanitizedValue: sanitized };
};

// Name validation (no numbers, reasonable length)
export const validateName = (value: string): ValidationResult => {
  const sanitized = String(value || '').trim();
  
  if (!sanitized) {
    return { isValid: false, errors: ['Name is required'], sanitizedValue: '' };
  }
  
  if (/\d/.test(sanitized)) {
    return { 
      isValid: false, 
      errors: ['Name cannot contain numbers'], 
      sanitizedValue: sanitized 
    };
  }
  
  if (sanitized.length > 100) {
    return { 
      isValid: false, 
      errors: ['Name cannot exceed 100 characters'], 
      sanitizedValue: sanitized.substring(0, 100) 
    };
  }
  
  return { isValid: true, errors: [], sanitizedValue: sanitized.toUpperCase() };
};

// Initials validation (2-4 characters, letters only)
export const validateInitials = (value: string): ValidationResult => {
  const sanitized = String(value || '').trim().toUpperCase();
  
  if (!sanitized) {
    return { isValid: false, errors: ['Initials are required'], sanitizedValue: '' };
  }
  
  if (!/^[A-Z]{1,4}$/.test(sanitized)) {
    return { 
      isValid: false, 
      errors: ['Initials must be 1-4 letters only'], 
      sanitizedValue: sanitized 
    };
  }
  
  return { isValid: true, errors: [], sanitizedValue: sanitized };
};

// Legal description validation (multi-line text)
export const validateLegalDescription = (value: string): ValidationResult => {
  const sanitized = String(value || '').trim();
  
  if (sanitized.length > 1000) {
    return { 
      isValid: false, 
      errors: ['Legal description cannot exceed 1000 characters'], 
      sanitizedValue: sanitized.substring(0, 1000) 
    };
  }
  
  // Replace line breaks with proper formatting for PDF
  const formatted = sanitized.replace(/\n/g, ' ').replace(/\s+/g, ' ');
  
  return { isValid: true, errors: [], sanitizedValue: formatted.toUpperCase() };
};