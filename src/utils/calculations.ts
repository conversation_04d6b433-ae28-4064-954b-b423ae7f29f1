import { FormData } from '../types/FormTypes';
import { formatDataForNewAPI as formatDataForAPI } from './pdfFieldMappings';

export const calculateFinancingAmount = (formData: FormData): number => {
  if (formData.paymentMethod.paymentType === 'cash') {
    return 0;
  }
  
  const { purchasePrice, initialDeposit } = formData.financialInfo;
  return Math.max(0, purchasePrice - initialDeposit);
};

export const updateFinancialCalculations = (formData: FormData): FormData => {
  const financingAmount = calculateFinancingAmount(formData);
  
  return {
    ...formData,
    paymentMethod: {
      ...formData.paymentMethod,
      financingAmount,
    },
  };
};

export const generateAutoFields = (formData: FormData): FormData => {
  const countyTaxId = formData.propertyInfo.county 
    ? `${formData.propertyInfo.county.toUpperCase()}, Florida Property Tax ID: [TAX ID NUMBER]`
    : '';

  const multipleBuyerInitials = formData.partyDetails.buyerInitials 
    ? Array(5).fill(formData.partyDetails.buyerInitials)
    : [];

  const multipleSellerInitials = formData.partyDetails.sellerInitials 
    ? Array(5).fill(formData.partyDetails.sellerInitials)
    : [];

  const multipleDateFields = formData.partyDetails.contractDate 
    ? Array(4).fill(formData.partyDetails.contractDate)
    : [];

  const additionalTermsForPDF = formData.additionalTerms
    .filter(term => term.trim())
    .map((term, index) => `[ADDITIONAL TERMS ${index + 1}]: ${term}`);

  return {
    ...formData,
    autoGeneratedFields: {
      countyTaxId,
      multipleBuyerInitials,
      multipleSellerInitials,
      multipleDateFields,
      additionalTerms: additionalTermsForPDF,
    },
  };
};

export const validateForm = (formData: FormData): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  // Property Information validation
  if (!formData.propertyInfo.sellerName.trim()) errors.push('Seller name is required');
  if (!formData.propertyInfo.buyerName.trim()) errors.push('Buyer name is required');
  if (!formData.propertyInfo.streetAddress.trim()) errors.push('Street address is required');
  if (!formData.propertyInfo.county.trim()) errors.push('County is required');
  if (!formData.propertyInfo.legalDescription1.trim()) errors.push('Legal description is required');
  if (!formData.propertyInfo.propertyTaxId.trim()) errors.push('Property tax ID is required');
  
  // Financial Information validation
  if (formData.financialInfo.purchasePrice <= 0) errors.push('Purchase price must be greater than 0');
  if (formData.financialInfo.initialDeposit < 0) errors.push('Initial deposit cannot be negative');
  if (formData.financialInfo.balanceToClose < 0) errors.push('Balance to close cannot be negative');
  if (!formData.financialInfo.closingDate) errors.push('Closing date is required');
  
  // Payment Method validation
  if (formData.paymentMethod.paymentType === 'financing') {
    if (!formData.paymentMethod.loanType) errors.push('Loan type is required for financing');
    if (!formData.paymentMethod.interestRateType) errors.push('Interest rate type is required for financing');
  }
  
  // Escrow Info validation
  if (!formData.escrowInfo.escrowAgentName.trim()) errors.push('Escrow agent name is required');
  if (!formData.escrowInfo.escrowAgentAddress.trim()) errors.push('Escrow agent address is required');
  
  // Party Details validation
  if (!formData.partyDetails.contractDate) errors.push('Contract date is required');
  if (!formData.partyDetails.buyerInitials.trim()) errors.push('Buyer initials are required');
  if (!formData.partyDetails.sellerInitials.trim()) errors.push('Seller initials are required');
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

<<<<<<< HEAD
export const formatDataForNewAPI = (formData: FormData) => {
  const financing = {
    cash: formData.paymentMethod.paymentType === 'cash',
    conventional: formData.paymentMethod.loanType === 'conventional',
    fha: formData.paymentMethod.loanType === 'fha',
    va: formData.paymentMethod.loanType === 'va',
    usda: formData.paymentMethod.loanType === 'usda',
    other: formData.paymentMethod.loanType === 'other',
  };

  const inspections = {
    as_is_condition: true, // Assuming default
    buyer_inspection: true, // Assuming default
    professional_inspection: true, // Assuming default
    termite_inspection: true, // Assuming default
    roof_inspection: true, // Assuming default
  };

  const appliances = {
    refrigerator: formData.propertyInfo.personalPropertyIncluded.toLowerCase().includes('refrigerator'),
    washer: formData.propertyInfo.personalPropertyIncluded.toLowerCase().includes('washer'),
    dryer: formData.propertyInfo.personalPropertyIncluded.toLowerCase().includes('dryer'),
    dishwasher: formData.propertyInfo.personalPropertyIncluded.toLowerCase().includes('dishwasher'),
    microwave: formData.propertyInfo.personalPropertyIncluded.toLowerCase().includes('microwave'),
    oven_range: formData.propertyInfo.personalPropertyIncluded.toLowerCase().includes('oven'),
  };

  return {
    seller_name: formData.propertyInfo.sellerName,
    buyer_name: formData.propertyInfo.buyerName,
    purchase_price: formData.financialInfo.purchasePrice,
    property_address: formData.propertyInfo.streetAddress,
    closing_date: formData.financialInfo.closingDate,
    financing: financing,
    loan_amount: formData.paymentMethod.financingAmount,
    down_payment: formData.financialInfo.initialDeposit,
    inspections: inspections,
    appliances: appliances,
  };
};
=======
export const formatFormDataForPDF = (formData: FormData) => {
  console.log('=== FORMATTING FORM DATA FOR PDF ===');
  console.log('Input Form Data:');
  console.log('- Seller Name:', formData.propertyInfo.sellerName);
  console.log('- Buyer Name:', formData.propertyInfo.buyerName);
  console.log('- Property Address:', formData.propertyInfo.streetAddress);
  console.log('- County:', formData.propertyInfo.county);
  console.log('- Property Tax ID:', formData.propertyInfo.propertyTaxId);
  console.log('- Purchase Price:', formData.financialInfo.purchasePrice);
  console.log('=== END INPUT DATA ===');

  const processedData = generateAutoFields(updateFinancialCalculations(formData));

  // This function is deprecated - use formatDataForNewAPI instead
  return {};
};

// Export the new API formatting function
export const formatDataForNewAPI = formatDataForAPI;
