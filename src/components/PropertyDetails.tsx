import React from 'react';
import { FileText, Home, Package } from 'lucide-react';
import { PropertyInfo as PropertyInfoType } from '../types/FormTypes';

interface PropertyDetailsProps {
  data: PropertyInfoType;
  onChange: (data: PropertyInfoType) => void;
}

const PropertyDetails: React.FC<PropertyDetailsProps> = ({ data, onChange }) => {
  const handleChange = (field: keyof PropertyInfoType, value: string) => {
    onChange({ ...data, [field]: value });
  };

  return (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mb-4">
          <FileText className="h-6 w-6 text-purple-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-800">Property Details</h3>
        <p className="text-gray-600">Legal description and included/excluded items</p>
      </div>

      {/* Legal Description */}
      <div className="space-y-4">
        <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
          <FileText className="h-4 w-4 text-purple-600" />
          Legal Description
        </label>
        <textarea
          value={data.legalDescription1}
          onChange={(e) => handleChange('legalDescription1', e.target.value)}
          rows={3}
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white resize-none"
          placeholder="Enter the legal description of the property"
          required
        />
        <textarea
          value={data.legalDescription2}
          onChange={(e) => handleChange('legalDescription2', e.target.value)}
          rows={2}
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white resize-none"
          placeholder="Additional legal description (optional)"
        />
      </div>

      {/* Property Inclusions/Exclusions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
            <Package className="h-4 w-4 text-green-600" />
            Personal Property Included
          </label>
          <textarea
            value={data.personalPropertyIncluded}
            onChange={(e) => handleChange('personalPropertyIncluded', e.target.value)}
            rows={4}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white resize-none"
            placeholder="List included personal property (appliances, fixtures, etc.)"
          />
        </div>

        <div className="space-y-2">
          <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
            <Package className="h-4 w-4 text-red-600" />
            Items Excluded
          </label>
          <textarea
            value={data.itemsExcluded}
            onChange={(e) => handleChange('itemsExcluded', e.target.value)}
            rows={4}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white resize-none"
            placeholder="List excluded items"
          />
        </div>
      </div>

      <div className="space-y-2">
        <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
          <Home className="h-4 w-4 text-blue-600" />
          Additional Personal Property
        </label>
        <textarea
          value={data.additionalPersonalProperty}
          onChange={(e) => handleChange('additionalPersonalProperty', e.target.value)}
          rows={3}
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white resize-none"
          placeholder="Additional personal property details"
        />
      </div>

      <div className="bg-purple-50 rounded-xl p-6 border border-purple-200">
        <h4 className="font-semibold text-purple-900 mb-2">Property Details Guide</h4>
        <div className="text-sm text-purple-800 space-y-1">
          <p>• <strong>Legal Description:</strong> The official property description from the deed</p>
          <p>• <strong>Included Items:</strong> Appliances, fixtures, and personal property that stay with the home</p>
          <p>• <strong>Excluded Items:</strong> Personal belongings that the seller will take</p>
        </div>
      </div>
    </div>
  );
};

export default PropertyDetails;