import React from 'react';
import { Plus, Minus, FileText } from 'lucide-react';

interface AdditionalTermsProps {
  data: string[];
  onChange: (data: string[]) => void;
}

const AdditionalTerms: React.FC<AdditionalTermsProps> = ({ data, onChange }) => {
  const handleTermChange = (index: number, value: string) => {
    const newTerms = [...data];
    newTerms[index] = value;
    onChange(newTerms);
  };

  const addTerm = () => {
    if (data.length < 16) {
      onChange([...data, '']);
    }
  };

  const removeTerm = (index: number) => {
    if (data.length > 1) {
      const newTerms = data.filter((_, i) => i !== index);
      onChange(newTerms);
    }
  };

  const filledTerms = data.filter(term => term.trim()).length;

  return (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mb-4">
          <FileText className="h-6 w-6 text-purple-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-800">Additional Terms & Conditions</h3>
        <p className="text-gray-600">Add any special terms or conditions for this purchase</p>
      </div>

      <div className="flex items-center justify-between mb-6">
        <div className="text-sm text-gray-600">
          <span className="font-semibold">{filledTerms}</span> of 16 terms used
        </div>
        <button
          type="button"
          onClick={addTerm}
          disabled={data.length >= 16}
          className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-xl hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-md hover:shadow-lg"
        >
          <Plus className="h-4 w-4" />
          Add Term
        </button>
      </div>

      <div className="space-y-4">
        {data.map((term, index) => (
          <div key={index} className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <span className="text-sm font-semibold text-purple-600">{index + 1}</span>
              </div>
              <div className="flex-1">
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Additional Term {index + 1}
                </label>
                <textarea
                  value={term}
                  onChange={(e) => handleTermChange(index, e.target.value)}
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white resize-none"
                  placeholder="Enter additional terms, conditions, or special agreements..."
                />
              </div>
              {data.length > 1 && (
                <button
                  type="button"
                  onClick={() => removeTerm(index)}
                  className="flex-shrink-0 p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                >
                  <Minus className="h-4 w-4" />
                </button>
              )}
            </div>
          </div>
        ))}
      </div>

      {data.length < 16 && (
        <div className="text-center">
          <button
            type="button"
            onClick={addTerm}
            className="inline-flex items-center gap-2 px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-all duration-200"
          >
            <Plus className="h-4 w-4" />
            Add Another Term
          </button>
        </div>
      )}

      <div className="bg-purple-50 rounded-xl p-6 border border-purple-200">
        <h4 className="font-semibold text-purple-900 mb-2">About Additional Terms</h4>
        <p className="text-sm text-purple-800">
          Each term will be mapped to a separate field in the PDF document (ADDITIONAL TERMS 1-16). 
          Use these fields for special conditions, contingencies, or any other agreements between buyer and seller.
        </p>
      </div>
    </div>
  );
};

export default AdditionalTerms;