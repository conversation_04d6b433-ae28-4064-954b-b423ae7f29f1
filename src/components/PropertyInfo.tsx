import React from 'react';
import { Home, MapPin, FileText, CheckCircle } from 'lucide-react';
import { PropertyInfo as PropertyInfoType } from '../types/FormTypes';
import { validateTaxId, validateAddress, validateName } from '../utils/pdfFieldMappings';

interface PropertyInfoProps {
  data: PropertyInfoType;
  onChange: (data: PropertyInfoType) => void;
}

const PropertyInfo: React.FC<PropertyInfoProps> = ({ data, onChange }) => {
  const handleChange = (field: keyof PropertyInfoType, value: string) => {
    // Apply validation and sanitization
    let sanitizedValue = value;
    let isValid = true;
    
    switch (field) {
      case 'propertyTaxId':
        const taxIdValidation = validateTaxId(value);
        sanitizedValue = taxIdValidation.sanitizedValue;
        isValid = taxIdValidation.isValid;
        break;
      case 'streetAddress':
        const addressValidation = validateAddress(value);
        sanitizedValue = addressValidation.sanitizedValue;
        isValid = addressValidation.isValid;
        break;
      case 'sellerName':
      case 'buyerName':
        const nameValidation = validateName(value);
        sanitizedValue = nameValidation.sanitizedValue;
        isValid = nameValidation.isValid;
        break;
      default:
        sanitizedValue = value;
    }
    
    onChange({ ...data, [field]: sanitizedValue });
  };

  return (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mb-4">
          <Home className="h-6 w-6 text-blue-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-800">Property & Parties</h3>
        <p className="text-gray-600">Basic property information and party details</p>
      </div>

      {/* Parties Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
            <Home className="h-4 w-4 text-blue-600" />
            Seller Name(s)
          </label>
          <input
            type="text"
            value={data.sellerName}
            onChange={(e) => handleChange('sellerName', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
            placeholder="John Doe"
            required
          />
          <p className="text-xs text-gray-500">This will appear as the seller in the PARTIES section</p>
        </div>

        <div className="space-y-2">
          <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
            <Home className="h-4 w-4 text-green-600" />
            Buyer Name(s)
          </label>
          <input
            type="text"
            value={data.buyerName}
            onChange={(e) => handleChange('buyerName', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
            placeholder="Jane Smith"
            required
          />
          <p className="text-xs text-gray-500">This will appear as the buyer in the PARTIES section</p>
        </div>
      </div>

      {/* Property Address */}
      <div className="space-y-2">
        <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
          <MapPin className="h-4 w-4 text-red-600" />
          Property Address
          {data.streetAddress && (
            <CheckCircle className="h-4 w-4 text-green-500" />
          )}
        </label>
        <input
          type="text"
          value={data.streetAddress}
          onChange={(e) => handleChange('streetAddress', e.target.value)}
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
          placeholder="123 Main Street, Miami, FL 33101"
          required
        />
      </div>

      {/* County and Tax ID */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
            County
            {data.county && (
              <CheckCircle className="h-4 w-4 text-green-500" />
            )}
          </label>
          <select
            value={data.county}
            onChange={(e) => handleChange('county', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
            required
          >
            <option value="">Select County</option>
            <option value="Miami-Dade">Miami-Dade</option>
            <option value="Broward">Broward</option>
            <option value="Palm Beach">Palm Beach</option>
            <option value="Orange">Orange</option>
            <option value="Hillsborough">Hillsborough</option>
            <option value="Pinellas">Pinellas</option>
            <option value="Duval">Duval</option>
            <option value="Lee">Lee</option>
            <option value="Polk">Polk</option>
            <option value="Brevard">Brevard</option>
            <option value="Other">Other</option>
          </select>
        </div>

        <div className="space-y-2">
          <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
            Property Tax ID
            {data.propertyTaxId && (
              <CheckCircle className="h-4 w-4 text-green-500" />
            )}
          </label>
          <input
            type="text"
            value={data.propertyTaxId}
            onChange={(e) => handleChange('propertyTaxId', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
            placeholder="12-3456789"
            required
          />
          <p className="text-xs text-gray-500">Format: XX-XXXXXXX (separate from county field)</p>
        </div>
      </div>

      {/* Legal Description */}
      <div className="space-y-4">
        <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
          <FileText className="h-4 w-4 text-purple-600" />
          Legal Description
          {data.legalDescription1 && (
            <CheckCircle className="h-4 w-4 text-green-500" />
          )}
        </label>
        <textarea
          value={data.legalDescription1}
          onChange={(e) => handleChange('legalDescription1', e.target.value)}
          rows={3}
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white resize-none"
          placeholder="Enter the legal description of the property"
          required
        />
        <textarea
          value={data.legalDescription2}
          onChange={(e) => handleChange('legalDescription2', e.target.value)}
          rows={2}
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white resize-none"
          placeholder="Additional legal description (optional)"
        />
      </div>

      {/* Auto-filled indicator */}
      {(data.streetAddress || data.county || data.propertyTaxId || data.legalDescription1) && (
        <div className="bg-green-50 rounded-xl p-4 border border-green-200">
          <div className="flex items-center gap-2 text-green-800 text-sm">
            <CheckCircle className="h-4 w-4" />
            <span className="font-medium">Some fields have been auto-filled from property data</span>
          </div>
          <p className="text-green-700 text-xs mt-1">
            You can still edit any of these fields manually if needed.
          </p>
        </div>
      )}

      <div className="bg-blue-50 rounded-xl p-6 border border-blue-200">
        <h4 className="font-semibold text-blue-900 mb-2">Next Steps</h4>
        <p className="text-sm text-blue-800">
          The PARTIES field will be formatted as: "SELLER NAME ("Seller"), and BUYER NAME ("Buyer")" in the PDF.
          County and Property Tax ID will be mapped to separate fields in the document.
        </p>
      </div>
    </div>
  );
};

export default PropertyInfo;