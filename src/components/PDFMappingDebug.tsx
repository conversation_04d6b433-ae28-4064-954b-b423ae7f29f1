import React from 'react';
import { FormData } from '../types/FormTypes';
import { formatDataForNewAPI } from '../utils/calculations';

interface PDFMappingDebugProps {
  formData: FormData;
}

const PDFMappingDebug: React.FC<PDFMappingDebugProps> = ({ formData }) => {
  const apiData = formatDataForNewAPI(formData);

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 max-h-96 overflow-y-auto">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">API Payload Debug</h3>
      <pre className="text-xs bg-gray-100 p-4 rounded-lg overflow-x-auto">
        {JSON.stringify(apiData, null, 2)}
      </pre>
    </div>
  );
};

export default PDFMappingDebug;
