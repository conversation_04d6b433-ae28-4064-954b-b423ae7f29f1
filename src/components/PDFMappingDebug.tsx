import React from 'react';
import { FormData } from '../types/FormTypes';
import { createValidatedPDFMapping } from '../utils/pdfFieldMappings';

interface PDFMappingDebugProps {
  formData: FormData;
}

const PDFMappingDebug: React.FC<PDFMappingDebugProps> = ({ formData }) => {
  const { mappings, errors } = createValidatedPDFMapping(formData);
  
  // Group mappings by type for better display
  const textMappings: { [key: string]: any } = {};
  const checkboxMappings: { [key: string]: boolean } = {};
  
  Object.entries(mappings).forEach(([key, value]) => {
    if (typeof value === 'boolean') {
      checkboxMappings[key] = value;
    } else {
      textMappings[key] = value;
    }
  });
  
  // Critical fields to highlight
  const criticalFields = [
    'PARTIES',
    'a Street address city zip',
    'County Florida Property Tax ID',
    'b Located in',
    'Text79', 'Text80', 'Text82', 'Text84',
    'Escrow Agent Information Name',
    'Date',
    'Buyers Initials',
    'Sellers Initials'
  ];
  
  return (
    <div className="bg-white rounded-xl shadow-lg p-6 max-h-96 overflow-y-auto">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">PDF Field Mapping Debug</h3>
      
      {/* Validation Errors */}
      {errors.length > 0 && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <h4 className="font-medium text-red-800 mb-2">Validation Errors:</h4>
          <ul className="text-sm text-red-700 space-y-1">
            {errors.map((error, index) => (
              <li key={index}>• {error}</li>
            ))}
          </ul>
        </div>
      )}
      
      {/* Critical Fields */}
      <div className="mb-6">
        <h4 className="font-medium text-gray-800 mb-3">Critical Fields:</h4>
        <div className="space-y-2">
          {criticalFields.map(fieldName => {
            const value = mappings[fieldName];
            const hasValue = value !== undefined && value !== null && value !== '';
            
            return (
              <div key={fieldName} className={`p-2 rounded text-sm ${hasValue ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                <div className="flex justify-between items-start">
                  <span className="font-mono text-xs text-gray-600">{fieldName}</span>
                  <span className={`text-xs ${hasValue ? 'text-green-600' : 'text-red-600'}`}>
                    {hasValue ? '✓' : '✗'}
                  </span>
                </div>
                <div className="mt-1 text-gray-800">
                  {typeof value === 'boolean' ? (value ? 'TRUE' : 'FALSE') : (value || 'EMPTY')}
                </div>
              </div>
            );
          })}
        </div>
      </div>
      
      {/* Text Fields */}
      <div className="mb-6">
        <h4 className="font-medium text-gray-800 mb-3">Text Fields ({Object.keys(textMappings).length}):</h4>
        <div className="space-y-1 max-h-40 overflow-y-auto">
          {Object.entries(textMappings).map(([key, value]) => (
            <div key={key} className="flex justify-between items-start p-1 text-xs border-b border-gray-100">
              <span className="font-mono text-gray-600 truncate flex-1 mr-2">{key}</span>
              <span className="text-gray-800 text-right max-w-xs truncate">
                {value || 'EMPTY'}
              </span>
            </div>
          ))}
        </div>
      </div>
      
      {/* Checkbox Fields */}
      <div>
        <h4 className="font-medium text-gray-800 mb-3">Checkbox Fields ({Object.keys(checkboxMappings).length}):</h4>
        <div className="space-y-1 max-h-40 overflow-y-auto">
          {Object.entries(checkboxMappings).map(([key, value]) => (
            <div key={key} className="flex justify-between items-center p-1 text-xs border-b border-gray-100">
              <span className="font-mono text-gray-600 truncate flex-1 mr-2">{key}</span>
              <span className={`font-medium ${value ? 'text-green-600' : 'text-gray-400'}`}>
                {value ? 'CHECKED' : 'UNCHECKED'}
              </span>
            </div>
          ))}
        </div>
      </div>
      
      {/* Summary */}
      <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="text-sm text-blue-800">
          <strong>Summary:</strong> {Object.keys(mappings).length} total fields mapped
          ({Object.keys(textMappings).length} text, {Object.keys(checkboxMappings).length} checkboxes)
          {errors.length > 0 && `, ${errors.length} validation errors`}
        </div>
      </div>
    </div>
  );
};

export default PDFMappingDebug;
