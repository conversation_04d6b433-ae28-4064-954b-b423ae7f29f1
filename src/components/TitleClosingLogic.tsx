import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, User<PERSON><PERSON><PERSON>, CheckCircle } from 'lucide-react';
import { TitleClosingLogic as TitleClosingLogicType } from '../types/FormTypes';

interface TitleClosingLogicProps {
  data: TitleClosingLogicType;
  onChange: (data: TitleClosingLogicType) => void;
}

const TitleClosingLogic: React.FC<TitleClosingLogicProps> = ({ data, onChange }) => {
  const handleClosingOptionChange = (option: 'seller-designates' | 'buyer-designates') => {
    onChange({
      ...data,
      closingOption: option,
      sellerDesignatesClosingAgent: option === 'seller-designates',
      buyerDesignatesClosingAgent: option === 'buyer-designates',
    });
  };

  return (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-12 h-12 bg-teal-100 rounded-full mb-4">
          <FileCheck className="h-6 w-6 text-teal-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-800">Title & Closing Responsibility</h3>
        <p className="text-gray-600">Who will designate the closing agent?</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div
          className={`relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-200 ${
            data.closingOption === 'seller-designates'
              ? 'border-teal-500 bg-teal-50 shadow-lg'
              : 'border-gray-200 hover:border-gray-300 bg-white hover:shadow-md'
          }`}
          onClick={() => handleClosingOptionChange('seller-designates')}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`p-3 rounded-full ${data.closingOption === 'seller-designates' ? 'bg-teal-100' : 'bg-gray-100'}`}>
                <UserCheck className={`h-6 w-6 ${data.closingOption === 'seller-designates' ? 'text-teal-600' : 'text-gray-600'}`} />
              </div>
              <div>
                <div className="font-semibold text-gray-900">Seller Designates</div>
                <div className="text-sm text-gray-600">Seller chooses closing agent</div>
              </div>
            </div>
            {data.closingOption === 'seller-designates' && (
              <CheckCircle className="h-6 w-6 text-teal-600" />
            )}
          </div>
        </div>

        <div
          className={`relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-200 ${
            data.closingOption === 'buyer-designates'
              ? 'border-blue-500 bg-blue-50 shadow-lg'
              : 'border-gray-200 hover:border-gray-300 bg-white hover:shadow-md'
          }`}
          onClick={() => handleClosingOptionChange('buyer-designates')}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`p-3 rounded-full ${data.closingOption === 'buyer-designates' ? 'bg-blue-100' : 'bg-gray-100'}`}>
                <UserCheck className={`h-6 w-6 ${data.closingOption === 'buyer-designates' ? 'text-blue-600' : 'text-gray-600'}`} />
              </div>
              <div>
                <div className="font-semibold text-gray-900">Buyer Designates</div>
                <div className="text-sm text-gray-600">Buyer chooses closing agent</div>
              </div>
            </div>
            {data.closingOption === 'buyer-designates' && (
              <CheckCircle className="h-6 w-6 text-blue-600" />
            )}
          </div>
        </div>
      </div>

      <div className="bg-gray-50 rounded-xl p-6">
        <h4 className="font-semibold text-gray-800 mb-3">What this means:</h4>
        <div className="space-y-2 text-sm text-gray-600">
          {data.closingOption === 'seller-designates' ? (
            <>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-teal-500 rounded-full"></div>
                <span>The seller will choose and designate the closing agent/title company</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-teal-500 rounded-full"></div>
                <span>Escrow information will be filled as seller's responsibility</span>
              </div>
            </>
          ) : data.closingOption === 'buyer-designates' ? (
            <>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>The buyer will choose and designate the closing agent/title company</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>Escrow information will be filled as buyer's responsibility</span>
              </div>
            </>
          ) : (
            <div className="text-gray-500 italic">Please select who will designate the closing agent</div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TitleClosingLogic;