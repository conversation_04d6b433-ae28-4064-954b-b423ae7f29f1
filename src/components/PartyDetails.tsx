import React from 'react';
import { Users, Calendar, User } from 'lucide-react';
import { PartyDetails as PartyDetailsType } from '../types/FormTypes';
import { validateInitials, validateDate } from '../utils/pdfFieldMappings';

interface PartyDetailsProps {
  data: PartyDetailsType;
  onChange: (data: PartyDetailsType) => void;
}

const PartyDetails: React.FC<PartyDetailsProps> = ({ data, onChange }) => {
  const handleChange = (field: keyof PartyDetailsType, value: string) => {
    // Apply validation based on field type
    let sanitizedValue = value;
    
    switch (field) {
      case 'buyerInitials':
      case 'sellerInitials':
        const initialsValidation = validateInitials(value);
        sanitizedValue = initialsValidation.sanitizedValue;
        break;
      case 'contractDate':
        const dateValidation = validateDate(value);
        sanitizedValue = dateValidation.sanitizedValue;
        break;
      default:
        sanitizedValue = value;
    }
    
    onChange({ ...data, [field]: sanitizedValue });
  };

  return (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-12 h-12 bg-orange-100 rounded-full mb-4">
          <Users className="h-6 w-6 text-orange-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-800">Contract Details</h3>
        <p className="text-gray-600">Enter the contract date and party initials</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="space-y-2">
          <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
            <Calendar className="h-4 w-4 text-blue-600" />
            Contract Date
          </label>
          <input
            type="date"
            value={data.contractDate}
            onChange={(e) => handleChange('contractDate', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white text-lg"
            required
          />
        </div>

        <div className="space-y-2">
          <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
            <User className="h-4 w-4 text-green-600" />
            Buyer Initials
          </label>
          <input
            type="text"
            value={data.buyerInitials}
            onChange={(e) => handleChange('buyerInitials', e.target.value.toUpperCase())}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white text-lg font-semibold text-center"
            maxLength={4}
            placeholder="AB"
            required
          />
        </div>

        <div className="space-y-2">
          <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
            <User className="h-4 w-4 text-purple-600" />
            Seller Initials
          </label>
          <input
            type="text"
            value={data.sellerInitials}
            onChange={(e) => handleChange('sellerInitials', e.target.value.toUpperCase())}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white text-lg font-semibold text-center"
            maxLength={4}
            placeholder="JS"
            required
          />
        </div>
      </div>

      <div className="bg-orange-50 rounded-xl p-6 border border-orange-200">
        <h4 className="font-semibold text-orange-900 mb-2">Initials Usage</h4>
        <p className="text-sm text-orange-800">
          These initials will be automatically populated throughout the purchase agreement document 
          wherever buyer and seller acknowledgments are required.
        </p>
      </div>
    </div>
  );
};

export default PartyDetails;