import React from 'react';
import { CreditCard, Banknote, TrendingUp, CheckCircle } from 'lucide-react';
import { PaymentMethod as PaymentMethodType } from '../types/FormTypes';

interface PaymentMethodProps {
  data: PaymentMethodType;
  onChange: (data: PaymentMethodType) => void;
  purchasePrice: number;
  initialDeposit: number;
}

const PaymentMethod: React.FC<PaymentMethodProps> = ({ data, onChange, purchasePrice, initialDeposit }) => {
  const handlePaymentTypeChange = (paymentType: 'cash' | 'financing') => {
    const financingAmount = paymentType === 'financing' ? purchasePrice - initialDeposit : 0;
    
    onChange({
      ...data,
      paymentType,
      loanType: paymentType === 'cash' ? '' : data.loanType,
      interestRateType: paymentType === 'cash' ? '' : data.interestRateType,
      financingAmount,
    });
  };

  const handleChange = (field: keyof PaymentMethodType, value: string) => {
    onChange({ ...data, [field]: value });
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  return (
    <div className="space-y-8">
      {/* Payment Type Selection */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-800">How will this purchase be funded?</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div
            className={`relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-200 ${
              data.paymentType === 'cash'
                ? 'border-green-500 bg-green-50 shadow-lg'
                : 'border-gray-200 hover:border-gray-300 bg-white hover:shadow-md'
            }`}
            onClick={() => handlePaymentTypeChange('cash')}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className={`p-3 rounded-full ${data.paymentType === 'cash' ? 'bg-green-100' : 'bg-gray-100'}`}>
                  <Banknote className={`h-6 w-6 ${data.paymentType === 'cash' ? 'text-green-600' : 'text-gray-600'}`} />
                </div>
                <div>
                  <div className="font-semibold text-gray-900">Cash Purchase</div>
                  <div className="text-sm text-gray-600">Pay the full amount in cash</div>
                </div>
              </div>
              {data.paymentType === 'cash' && (
                <CheckCircle className="h-6 w-6 text-green-600" />
              )}
            </div>
          </div>

          <div
            className={`relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-200 ${
              data.paymentType === 'financing'
                ? 'border-blue-500 bg-blue-50 shadow-lg'
                : 'border-gray-200 hover:border-gray-300 bg-white hover:shadow-md'
            }`}
            onClick={() => handlePaymentTypeChange('financing')}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className={`p-3 rounded-full ${data.paymentType === 'financing' ? 'bg-blue-100' : 'bg-gray-100'}`}>
                  <CreditCard className={`h-6 w-6 ${data.paymentType === 'financing' ? 'text-blue-600' : 'text-gray-600'}`} />
                </div>
                <div>
                  <div className="font-semibold text-gray-900">Financing Required</div>
                  <div className="text-sm text-gray-600">Use a mortgage loan</div>
                </div>
              </div>
              {data.paymentType === 'financing' && (
                <CheckCircle className="h-6 w-6 text-blue-600" />
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Financing Details */}
      {data.paymentType === 'financing' && (
        <div className="bg-blue-50 rounded-xl p-6 space-y-6">
          <h4 className="text-lg font-semibold text-blue-900">Financing Details</h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <label className="text-sm font-semibold text-gray-700">Loan Type</label>
              <select
                value={data.loanType}
                onChange={(e) => handleChange('loanType', e.target.value)}
                className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white"
                required
              >
                <option value="">Select loan type</option>
                <option value="Conventional">Conventional</option>
                <option value="FHA">FHA</option>
                <option value="VA">VA</option>
                <option value="Other">Other</option>
              </select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-semibold text-gray-700">Interest Rate Type</label>
              <select
                value={data.interestRateType}
                onChange={(e) => handleChange('interestRateType', e.target.value)}
                className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white"
                required
              >
                <option value="">Select rate type</option>
                <option value="Fixed">Fixed Rate</option>
                <option value="Adjustable">Adjustable Rate</option>
              </select>
            </div>
          </div>

          {/* Financing Calculation */}
          <div className="bg-white rounded-xl p-6 border border-blue-200">
            <div className="flex items-center gap-3 mb-4">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <span className="font-semibold text-gray-800">Loan Amount Calculation</span>
            </div>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Purchase Price:</span>
                <span className="font-semibold">{formatCurrency(purchasePrice)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Initial Deposit:</span>
                <span className="font-semibold">- {formatCurrency(initialDeposit)}</span>
              </div>
              <div className="border-t pt-3 flex justify-between">
                <span className="font-semibold text-gray-800">Financing Amount:</span>
                <span className="text-xl font-bold text-blue-600">{formatCurrency(data.financingAmount)}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Cash Purchase Summary */}
      {data.paymentType === 'cash' && (
        <div className="bg-green-50 rounded-xl p-6">
          <div className="flex items-center gap-3 mb-4">
            <Banknote className="h-5 w-5 text-green-600" />
            <span className="font-semibold text-green-800">Cash Purchase Summary</span>
          </div>
          <div className="text-sm text-green-700">
            This purchase will be completed with cash. No financing contingencies will apply.
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentMethod;