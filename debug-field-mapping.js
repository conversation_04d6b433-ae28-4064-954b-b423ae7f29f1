#!/usr/bin/env node

// Debug script to test PDF field mappings
// This will help identify what fields are being mapped and their values

const mockFormData = {
  propertyInfo: {
    sellerName: "JOHN DOE",
    buyerName: "JANE SMITH", 
    streetAddress: "123 Main Street, Miami, FL 33101",
    county: "Miami-Dade",
    propertyTaxId: "12-3456789",
    legalDescription1: "LOT 1, BLOCK 1, MAIN STREET SUBDIVISION",
    legalDescription2: "PLAT BOOK 25, PAGE 45, MIAMI-DADE COUNTY RECORDS",
    personalPropertyIncluded: "Refrigerator, Oven, Washer, Dryer",
    additionalPersonalProperty: "Patio Furniture",
    itemsExcluded: "<PERSON><PERSON>'s personal artwork"
  },
  financialInfo: {
    purchasePrice: 500000,
    initialDeposit: 25000,
    balanceToClose: 170000,
    closingDate: new Date('2024-07-01')
  },
  paymentMethod: {
    financingAmount: 300000,
    paymentType: 'financing',
    loanType: 'Conventional',
    interestRateType: 'Fixed'
  },
  partyDetails: {
    buyerInitials: "JS",
    sellerInitials: "JD",
    contractDate: new Date('2024-06-01')
  },
  escrowInfo: {
    escrowAgentName: "ABC Title Company",
    escrowAgentAddress: "456 Title Street\nMiami, FL 33101"
  }
};

// Validation functions (simplified)
const validateText = (val, maxLength) => val && val.length <= maxLength;
const validateAddress = (val) => val && val.length > 0;
const validateTaxId = (val) => val && /^\d{2}-\d{7}$/.test(val);
const validateInitials = (val) => val && /^[A-Z]{1,4}$/.test(val);
const validateName = (val) => val && !/\d/.test(val);

// Currency formatting
const formatCurrency = (amount) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

// Date formatting
const formatDate = (date) => {
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

console.log('🔍 DEBUGGING PDF FIELD MAPPINGS');
console.log('================================');

// Test critical field mappings based on field-mapping-report.md

console.log('\n1. PARTIES FIELD:');
const sellerName = mockFormData.propertyInfo.sellerName.toUpperCase();
const buyerName = mockFormData.propertyInfo.buyerName.toUpperCase();
const partiesString = `${sellerName} ("Seller"), and ${buyerName} ("Buyer")`;
console.log(`   Field: "PARTIES"`);
console.log(`   Value: "${partiesString}"`);

console.log('\n2. PROPERTY FIELDS:');
console.log(`   Field: "a Street address city zip"`);
console.log(`   Value: "${mockFormData.propertyInfo.streetAddress}"`);

const countyString = `${mockFormData.propertyInfo.county}, Florida`;
console.log(`   Field: "County Florida Property Tax ID"`);
console.log(`   Value: "${countyString}"`);

console.log(`   Field: "b Located in"`);
console.log(`   Value: "${mockFormData.propertyInfo.propertyTaxId}"`);

console.log('\n3. FINANCIAL FIELDS:');
console.log(`   Field: "Text79" (Purchase Price)`);
console.log(`   Value: "${formatCurrency(mockFormData.financialInfo.purchasePrice)}"`);

console.log(`   Field: "Text80" (Initial Deposit)`);
console.log(`   Value: "${formatCurrency(mockFormData.financialInfo.initialDeposit)}"`);

console.log(`   Field: "Text82" (Financing Amount)`);
console.log(`   Value: "${formatCurrency(mockFormData.paymentMethod.financingAmount)}"`);

console.log(`   Field: "Text84" (Balance to Close)`);
console.log(`   Value: "${formatCurrency(mockFormData.financialInfo.balanceToClose)}"`);

console.log('\n4. INITIALS FIELDS:');
console.log(`   Field: "Buyers Initials"`);
console.log(`   Value: "${mockFormData.partyDetails.buyerInitials.toUpperCase()}"`);

console.log(`   Field: "Sellers Initials"`);
console.log(`   Value: "${mockFormData.partyDetails.sellerInitials.toUpperCase()}"`);

console.log('\n5. DATE FIELDS:');
console.log(`   Field: "Closing Date at the time established by the Closing Agent"`);
console.log(`   Value: "${formatDate(mockFormData.financialInfo.closingDate)}"`);

console.log(`   Field: "Date" (Contract Date)`);
console.log(`   Value: "${formatDate(mockFormData.partyDetails.contractDate)}"`);

console.log('\n6. ESCROW FIELDS:');
console.log(`   Field: "Escrow Agent Information Name"`);
console.log(`   Value: "${mockFormData.escrowInfo.escrowAgentName}"`);

console.log('\n================================');
console.log('✅ Field mapping debug complete');
console.log('\nNext steps:');
console.log('1. Compare these values with what appears in the generated PDF');
console.log('2. If fields are empty in PDF, the field names might be different');
console.log('3. Check server logs for "=== ALL PDF FIELD NAMES ===" section');
console.log('4. Update field names in pdfFieldMappings.ts if needed');
