export const fieldMap = {
  // Core Party Information
  sellerName: "PARTIES",
  buyerName: "undefined_2", // Primary buyer name field
  sellerInitials: "Sellers Initials",
  buyerInitials: "Buyers Initials",
  
  // Property Information
  propertyAddress: "a Street address city zip",
  taxId: "County Florida Property Tax ID",
  legalDescriptionLine1: "c Real Property The legal description is 1",
  legalDescriptionLine2: "c Real Property The legal description is 2",
  propertyDescription: "PROPERTY DESCRIPTION",
  
  // Financial Information
  purchasePrice: "Text79", // Main purchase price field
  depositAmount: "Text80", // Deposit amount
  loanAmount: "c Financing Express as a dollar amount or percentage Loan Amount see Paragraph 8",
  cashToClose: "Text81", // Cash due at closing
  balanceToFinance: "Text82", // Balance to be financed
  
  // Additional Financial Fields
  text83: "Text83",
  text84: "Text84",
  text85: "Text85",
  text86: "Text86",
  text87: "Text87",
  text88: "Text88",
  text89: "Text89",
  text90: "Text90",
  text91: "Text91",
  text92: "Text92",
  text93: "Text93",
  text94: "Text94",
  text95: "Text95",
  text96: "Text96",
  text103: "Text103",
  
  // Dates
  contractDate: "Date",
  closingDate: "Closing Date at the time established by the Closing Agent",
  date2: "Date_2",
  date3: "Date_3",
  date4: "Date_4",
  
  // Financing Checkboxes
  cashPurchase: "a Buyer will pay cash for the purchase of the Property at Closing There is no financing contingency to Buyers",
  financingContingency: "b This Contract is contingent upon Buyer obtaining approval of a",
  conventional: "conventional",
  fha: "FHA",
  va: "VA or",
  other: "other",
  fixedRate: "fixed",
  adjustableRate: "adjustable",
  
  // Closing and Title
  sellerDesignatesClosing: "i Seller shall designate Closing Agent and pay for Owners Policy and Charges and Buyer shall pay the",
  buyerDesignatesClosing: "ii Buyer shall designate Closing Agent and pay for Owners Policy and Charges and charges for closing",
  miamiDadeProvision: "iii MIAMIDADEBROWARD REGIONAL PROVISION Seller shall furnish a copy of a prior owners policy",
  
  // Escrow Information
  escrowAgentName: "Escrow Agent Information Name",
  escrowEmail: "Email",
  escrowFax: "Fax",
  escrowDeposit: "accompanies offer or ii",
  
  // Personal Property
  personalProperty: "Personal Property is included in the Purchase Price has no contributory value and shall be left for the Buyer",
  otherPersonalProperty: "Other Personal Property items included in this purchase are",
  excludedItems: "e The following items are excluded from the purchase",
  
  // Additional Terms
  additionalTerms1: "20 ADDITIONAL TERMS 1",
  additionalTerms2: "20 ADDITIONAL TERMS 2",
  additionalTerms3: "20 ADDITIONAL TERMS 3",
  additionalTerms4: "20 ADDITIONAL TERMS 4",
  additionalTerms5: "20 ADDITIONAL TERMS 5",
  additionalTerms6: "20 ADDITIONAL TERMS 6",
  additionalTerms7: "20 ADDITIONAL TERMS 7",
  additionalTerms8: "20 ADDITIONAL TERMS 8",
  additionalTerms9: "20 ADDITIONAL TERMS 9",
  additionalTerms10: "20 ADDITIONAL TERMS 10",
  additionalTerms11: "20 ADDITIONAL TERMS 11",
  additionalTerms12: "20 ADDITIONAL TERMS 12",
  additionalTerms13: "20 ADDITIONAL TERMS 13",
  additionalTerms14: "20 ADDITIONAL TERMS 14",
  additionalTerms15: "20 ADDITIONAL TERMS 15",
  additionalTerms16: "20 ADDITIONAL TERMS 16",
  
  // Contact Information
  buyerAddress1: "Buyers address for purposes of notice 1",
  buyerAddress2: "Buyers address for purposes of notice 2",
  buyerAddress3: "Buyers address for purposes of notice 3",
  sellerAddress1: "Sellers address for purposes of notice 1",
  sellerAddress2: "Sellers address for purposes of notice 2",
  sellerAddress3: "Sellers address for purposes of notice 3",
  
  // Broker Information
  listingBroker: "Listing Broker",
  listingSalesAssociate: "Listing Sales Associate",
  cooperatingBroker: "Cooperating Broker if any",
  cooperatingSalesAssociate: "Cooperating Sales Associate if any",
  
  // Multiple Seller/Buyer Initials
  sellerInitials2: "Sellers Initials_2",
  sellerInitials3: "Sellers Initials_3",
  sellerInitials4: "Sellers Initials_4",
  sellerInitials5: "Sellers Initials_5",
  sellerInitials6: "Sellers Initials_6",
  sellerInitials7: "Sellers Initials_7",
  sellerInitials8: "Sellers Initials_8",
  sellerInitials9: "Sellers Initials_9",
  sellerInitials10: "Sellers Initials_10",
  sellerInitials11: "Sellers Initials_11",
  buyerInitials2: "Buyers Initials_2",
  
  // Additional Buyer Name Fields
  buyerName2: "undefined_3",
  buyerName3: "undefined_4",
  buyerName4: "undefined_5",
  buyerName5: "undefined_6",
  buyerName6: "undefined_7",
  buyerName7: "undefined_8",
  buyerName8: "undefined_9",
  buyerName9: "undefined_10",
  buyerName10: "undefined_11",
  buyerName11: "undefined_12",
  buyerName12: "undefined_13",
  buyerName13: "undefined_14",
  buyerName14: "undefined_15",
  
  // Checkboxes for Various Contingencies and Riders
  condominiumRider: "A Condominium Rider",
  homeownersAssn: "B Homeowners Assn",
  sellerFinancing: "C Seller Financing",
  mortgageAssumption: "D Mortgage Assumption",
  fhaVaFinancing: "E FHAVA Financing",
  appraisalContingency: "F Appraisal Contingency",
  shortSale: "G Short Sale",
  homeownersFloodIns: "H HomeownersFlood Ins",
  defectiveDrywall: "M Defective Drywall",
  coastalConstructionControl: "N Coastal Construction Control",
  insulationDisclosure: "O Insulation Disclosure",
  leadPaintDisclosure: "P Lead Paint Disclosure Pre1978",
  housingForOlderPersons: "Q Housing for Older Persons",
  rezoning: "R Rezoning",
  leasePurchaseOption: "S Lease Purchase Lease Option",
  preClosingOccupancy: "T PreClosing Occupancy",
  saleOfBuyersProperty: "V Sale of Buyers Property",
  backupContract: "W Backup Contract",
  sellersAttorneyApproval: "Y Sellers Attorney Approval",
  buyersAttorneyApproval: "Z Buyers Attorney Approval",
  
  // Special Checkboxes
  checkbox97: "Check Box97",
  checkbox99: "Check Box99",
  checkbox101: "Check Box101",
  checkbox102: "Check Box102",
  
  // Time Periods
  ifLeftBlankThen5Days: "if left blank then 5 days",
  blankThen3DaysAfterEffectiveDate: "blank then 3 days after Effective Date IF NEITHER BOX IS CHECKED THEN",
  
  // Other Fields
  address: "Address",
  other2: "Other_2",
  other3: "Other_3",
  otherField: "Other",
  
  // Assignment Options
  mayAssignAndBeReleased: "may assign and thereby be released from any further liability under",
  mayAssignButNotReleased: "may assign but not be released from liability under this Contract or",
  mayNotAssign: "may not assign this",
  
  // Home Warranty
  homeWarrantyPlan: "NA shall pay for a home warranty plan issued by",
  warrantyPlanDescription: "warranty plan provides for repair or replacement of many of a homes mechanical systems and major builtin"
};
