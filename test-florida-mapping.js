const fetch = require('node-fetch');

// Test data that matches the FormData structure
const testFormData = {
  propertyInfo: {
    sellerName: 'JOHN SELLER',
    buyerName: 'JANE BUYER',
    streetAddress: '123 Ocean Drive, Miami Beach, FL 33139',
    county: 'Miami-Dade',
    propertyTaxId: '12-3456-789-0123',
    legalDescription1: 'Lot 15, Block 3, SUNSET ESTATES SUBDIVISION',
    legalDescription2: 'According to the plat thereof recorded in Plat Book 45, Page 67',
    personalPropertyIncluded: 'All kitchen appliances including refrigerator, washer, dryer, microwave, dishwasher',
    additionalPersonalProperty: 'Window treatments and ceiling fans',
    itemsExcluded: 'Outdoor furniture and artwork'
  },
  financialInfo: {
    purchasePrice: 750000,
    initialDeposit: 75000,
    balanceToClose: 675000,
    closingDate: '2025-08-15'
  },
  paymentMethod: {
    paymentType: 'financing',
    loanType: 'conventional',
    financingAmount: 600000,
    interestRateType: 'fixed'
  },
  partyDetails: {
    contractDate: '2025-07-15',
    sellerInitials: 'JS',
    buyerInitials: 'JB'
  },
  escrowInfo: {
    escrowAgentName: 'Sunshine Title Company',
    escrowAgentAddress: '456 Title Street, Miami, FL 33101'
  },
  titleClosingLogic: {
    closingOption: 'seller-designates'
  },
  additionalTerms: [
    'Property sold AS-IS with right to inspect',
    'Seller to provide termite inspection',
    'Buyer to verify all permits and certificates'
  ]
};

async function testMapping() {
  try {
    console.log('🧪 Testing FloridaTemplate mapping...');
    
    const response = await fetch('http://localhost:3001/test-mapping', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testFormData)
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const result = await response.json();
    
    console.log('\n✅ MAPPING TEST RESULTS:');
    console.log(`- Success: ${result.success}`);
    console.log(`- Fields mapped: ${result.fieldCount}`);
    console.log('\n📋 Sample mappings:');
    result.sampleMappings.forEach(([field, value]) => {
      console.log(`  "${field}" = "${value}"`);
    });
    
    return result;
    
  } catch (error) {
    console.error('❌ Mapping test failed:', error.message);
    return null;
  }
}

async function testPDFGeneration() {
  try {
    console.log('\n🎯 Testing PDF generation...');
    
    const response = await fetch('http://localhost:3001/generate-pdf', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testFormData)
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }
    
    const pdfBuffer = await response.buffer();
    
    console.log('✅ PDF GENERATION RESULTS:');
    console.log(`- PDF size: ${pdfBuffer.length} bytes`);
    console.log(`- Content type: ${response.headers.get('content-type')}`);
    
    // Save the PDF for inspection
    const fs = require('fs');
    fs.writeFileSync('test-output.pdf', pdfBuffer);
    console.log('- PDF saved as: test-output.pdf');
    
    return pdfBuffer.length > 0;
    
  } catch (error) {
    console.error('❌ PDF generation test failed:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting FloridaTemplate tests...');
  console.log('Make sure the server is running: npm run dev:server\n');
  
  // Test 1: Mapping
  const mappingResult = await testMapping();
  
  if (!mappingResult || !mappingResult.success) {
    console.log('❌ Mapping test failed - stopping tests');
    return;
  }
  
  // Test 2: PDF Generation
  const pdfResult = await testPDFGeneration();
  
  // Summary
  console.log('\n📊 TEST SUMMARY:');
  console.log(`- Mapping: ${mappingResult.success ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`- PDF Generation: ${pdfResult ? '✅ PASS' : '❌ FAIL'}`);
  
  if (mappingResult.success && pdfResult) {
    console.log('\n🎉 All tests passed! FloridaTemplate system is working.');
  } else {
    console.log('\n⚠️ Some tests failed. Check the server logs for details.');
  }
}

// Run the tests
runTests().catch(console.error);
