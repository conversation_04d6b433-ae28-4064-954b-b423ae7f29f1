// Debug script to check PDF field names
const fs = require('fs');
const path = require('path');
const { PDFDocument } = require('pdf-lib');

async function debugPDFFields() {
  try {
    console.log('=== PDF FIELD DEBUG SCRIPT ===');
    
    // Load the PDF template
    const templatePath = path.join(__dirname, 'public', 'Florida-As_Is-Purchase-Agreement.pdf');
    console.log('Loading template from:', templatePath);
    
    const existingPdfBytes = fs.readFileSync(templatePath);
    console.log('✓ Template loaded, size:', existingPdfBytes.length, 'bytes');
    
    // Load the PDF document
    const pdfDoc = await PDFDocument.load(existingPdfBytes);
    const form = pdfDoc.getForm();
    
    // Get all form fields
    const fields = form.getFields();
    console.log(`✓ Found ${fields.length} form fields in PDF`);
    
    // Group fields by type
    const textFields = [];
    const checkboxFields = [];
    const otherFields = [];
    
    fields.forEach(field => {
      const name = field.getName();
      const type = field.constructor.name;
      
      if (type === 'PDFTextField') {
        textFields.push(name);
      } else if (type === 'PDFCheckBox') {
        checkboxFields.push(name);
      } else {
        otherFields.push({ name, type });
      }
    });
    
    console.log('\n=== TEXT FIELDS ===');
    textFields.sort().forEach((name, index) => {
      console.log(`${index + 1}. "${name}"`);
    });
    
    console.log('\n=== CHECKBOX FIELDS ===');
    checkboxFields.sort().forEach((name, index) => {
      console.log(`${index + 1}. "${name}"`);
    });
    
    console.log('\n=== OTHER FIELDS ===');
    otherFields.forEach((field, index) => {
      console.log(`${index + 1}. "${field.name}" (${field.type})`);
    });
    
    // Look for specific critical fields
    console.log('\n=== CRITICAL FIELD CHECK ===');
    const criticalFields = [
      'PARTIES',
      'a Street address city zip',
      'County Florida Property Tax ID',
      'b Located in',
      'Text79', 'Text80', 'Text81', 'Text82', 'Text83', 'Text84',
      'Escrow Agent Information Name',
      'Address',
      'Date', 'Date_2', 'Date_3', 'Date_4',
      'Buyers Initials', 'Buyers Initials_2',
      'Sellers Initials', 'Sellers Initials_2'
    ];
    
    criticalFields.forEach(fieldName => {
      const exists = fields.some(field => field.getName() === fieldName);
      console.log(`${exists ? '✓' : '✗'} ${fieldName}`);
    });
    
    // Save field list to file for reference
    const fieldList = {
      textFields: textFields.sort(),
      checkboxFields: checkboxFields.sort(),
      otherFields: otherFields,
      totalFields: fields.length
    };
    
    fs.writeFileSync('pdf-field-list.json', JSON.stringify(fieldList, null, 2));
    console.log('\n✓ Field list saved to pdf-field-list.json');
    
  } catch (error) {
    console.error('Error:', error);
  }
}

debugPDFFields();
