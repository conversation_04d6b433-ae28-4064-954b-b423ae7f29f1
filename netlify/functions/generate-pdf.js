import fs from 'fs';
import path from 'path';
import { PDFDocument } from 'pdf-lib';

// FloridaTemplate.pdf field mapping
// Maps structured FormData to the exact PDF field names
function mapFormDataToFloridaTemplate(formData) {
  const mapping = {};

  // Handle both structured FormData and flat form data
  if (formData.propertyInfo) {
    // Structured FormData from React app
    mapping["'(Text_1)'"] = formData.propertyInfo.sellerName || '';
    mapping["'(Text_2)'"] = formData.propertyInfo.buyerName || '';
    mapping["'(Text_3)'"] = formData.propertyInfo.streetAddress || '';
    mapping["'(Text_4)'"] = formData.propertyInfo.county || '';
    mapping["'(Text_5)'"] = formData.propertyInfo.propertyTaxId || '';
    mapping["'(Text_6)'"] = formData.propertyInfo.legalDescription1 || '';
    mapping["'(Text_7)'"] = formData.propertyInfo.legalDescription2 || '';
    mapping["'(Text_9)'"] = formData.propertyInfo.personalPropertyIncluded || '';
    mapping["'(Text_10)'"] = formData.propertyInfo.additionalPersonalProperty || '';
    mapping["'(Text_11)'"] = formData.propertyInfo.itemsExcluded || '';

    // Financial info
    mapping["'(Number_1)'"] = formData.financialInfo.purchasePrice?.toString() || '';
    mapping["'(Number_2)'"] = formData.financialInfo.initialDeposit?.toString() || '';
    mapping["'(Number_3)'"] = formData.financialInfo.balanceToClose?.toString() || '';

    // Payment method checkboxes
    mapping["'(Checkbox_8)'"] = formData.paymentMethod.paymentType === 'cash';
    mapping["'(Checkbox_9)'"] = formData.paymentMethod.loanType === 'conventional';
    mapping["'(Checkbox_10)'"] = formData.paymentMethod.loanType === 'fha';
    mapping["'(Checkbox_11)'"] = formData.paymentMethod.loanType === 'va';

    // Dates
    mapping["'(Date_1)'"] = formData.partyDetails.contractDate || '';

    // Initials
    mapping["'(Initials_1)'"] = formData.partyDetails.sellerInitials || '';
    mapping["'(Initials_2)'"] = formData.partyDetails.buyerInitials || '';
  } else {
    // Flat form data - try to map common field names
    mapping["'(Text_1)'"] = formData.sellerName || formData.PARTIES || '';
    mapping["'(Text_2)'"] = formData.buyerName || formData.and || '';
    mapping["'(Text_3)'"] = formData.streetAddress || formData['a Street address city zip'] || '';
    mapping["'(Text_4)'"] = formData.county || '';
    mapping["'(Text_5)'"] = formData.propertyTaxId || formData['b Located in'] || '';
    mapping["'(Number_1)'"] = formData.purchasePrice || formData.Text79 || '';
    mapping["'(Number_2)'"] = formData.initialDeposit || formData.Text80 || '';
    mapping["'(Date_1)'"] = formData.contractDate || formData.Date || '';
  }

  return mapping;
}

export const handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: '',
    };
  }

  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' }),
    };
  }

  try {
    console.log('=== NETLIFY FUNCTION: PDF GENERATION STARTED ===');

    const formData = JSON.parse(event.body);
    console.log('Form data keys:', Object.keys(formData));
    console.log('Total fields received:', Object.keys(formData).length);

    // Enhanced debugging for critical fields
    console.log('=== CRITICAL FIELD VALUES RECEIVED ===');
    console.log('SELLER NAME (PARTIES field):', formData['PARTIES']);
    console.log('BUYER NAME (and field):', formData['and']);
    console.log('Property address:', formData['a Street address city zip']);
    console.log('County field:', formData['County Florida Property Tax ID']);
    console.log('Tax ID field:', formData['b Located in']);
    console.log('Purchase price (Text79):', formData['Text79']);
    console.log('Initial deposit (Text80):', formData['Text80']);
    console.log('Financing amount (Text82):', formData['Text82']);
    console.log('Balance to close (Text84):', formData['Text84']);
    console.log('Contract date:', formData['Date']);
    console.log('Closing date:', formData['Closing Date at the time established by the Closing Agent']);
    console.log('Buyer initials:', formData['Buyers Initials']);
    console.log('Seller initials:', formData['Sellers Initials']);
    
    // Try to load the FloridaTemplate.pdf from multiple locations
    let existingPdfBytes;
    const possiblePaths = [
      path.join(process.cwd(), 'public', 'FloridaTemplate.pdf'),
      path.join(process.cwd(), 'dist', 'FloridaTemplate.pdf'),
      path.join(process.cwd(), 'FloridaTemplate.pdf'),
      './FloridaTemplate.pdf'
    ];
    
    let templateFound = false;
    for (const templatePath of possiblePaths) {
      try {
        console.log('Checking template path:', templatePath);
        if (fs.existsSync(templatePath)) {
          existingPdfBytes = fs.readFileSync(templatePath);
          console.log('✓ Template loaded from:', templatePath, 'size:', existingPdfBytes.length, 'bytes');
          templateFound = true;
          break;
        }
      } catch (pathError) {
        console.log('Path not accessible:', templatePath, pathError.message);
      }
    }
    
    if (!templateFound) {
      throw new Error('PDF template not found in any expected location');
    }
    
    // Load the PDF document and form
    const pdfDoc = await PDFDocument.load(existingPdfBytes);
    const form = pdfDoc.getForm();
    
    // Get all form fields
    const fields = form.getFields();
    console.log(`✓ Found ${fields.length} form fields in PDF`);

    // Log ALL field names for debugging
    console.log('=== ALL PDF FIELD NAMES ===');
    fields.forEach(field => {
      console.log(`  - "${field.getName()}" (${field.constructor.name})`);
    });
    
    // Helper function to safely set text field (same as localhost)
    function setTextField(fieldName, value) {
      try {
        const field = form.getTextField(fieldName);
        let textValue = String(value || '').trim();
        
        // CRITICAL VALIDATION: Prevent data type mismatches (same as localhost)
        
        // 1. Address fields - should NEVER contain currency values
        if (fieldName === 'a Street address city zip' || fieldName.toLowerCase().includes('address')) {
          if (/^\$/.test(textValue) || /^\d+(\.\d{2})?$/.test(textValue) || /^\d{1,3}(,\d{3})*(\.\d{2})?$/.test(textValue)) {
            console.error(`❌ CRITICAL ERROR: Invalid data in address field "${fieldName}": "${textValue}"`);
            textValue = ''; // Clear invalid data
          }
        }
        
        // 2. County field - should contain only county name + Florida
        if (fieldName === 'County Florida Property Tax ID') {
          if (/^\$/.test(textValue) || /\d{2}-\d+/.test(textValue)) {
            console.error(`❌ CRITICAL ERROR: Invalid data in county field "${fieldName}": "${textValue}"`);
            textValue = '';
          }
        }

        // 3. Tax ID field (line b) - should contain only tax ID number
        if (fieldName === 'b Located in') {
          if (/^\$/.test(textValue) || textValue.toLowerCase().includes('county')) {
            console.error(`❌ CRITICAL ERROR: Invalid data in tax ID field "${fieldName}": "${textValue}"`);
            textValue = '';
          }
        }
        
        // 3. Currency fields - PRESERVE pre-formatted currency from client
        if (fieldName.startsWith('Text') && /^(79|80|81|82|83|84)$/.test(fieldName.replace('Text', ''))) {
          // If value is already formatted with $, keep it as-is
          if (textValue && textValue.includes('$')) {
            console.log(`✓ CURRENCY: Using pre-formatted value for ${fieldName}: "${textValue}"`);
          } else if (textValue && !isNaN(parseFloat(textValue))) {
            // Only format if it's a raw number
            const numValue = parseFloat(textValue);
            textValue = `$${numValue.toLocaleString()}`;
            console.log(`✓ CURRENCY: Auto-formatted ${fieldName}: "${textValue}"`);
          }
        }
        
        // Character limits based on field type
        if (fieldName.includes('Initials')) {
          textValue = textValue.substring(0, 4);
        } else if (fieldName.includes('Date')) {
          // Accept both ISO format (2024-06-01) and readable format (June 1, 2024)
          if (textValue && !isValidDate(textValue) && !isReadableDate(textValue)) {
            console.warn(`Invalid date format for ${fieldName}: ${textValue}`);
            textValue = '';
          } else {
            console.log(`✓ DATE: Accepted date format for ${fieldName}: "${textValue}"`);
          }
        } else {
          textValue = textValue.substring(0, 500);
        }
        
        // Set the field value
        field.setText(textValue);
        
        // Enhanced formatting
        field.setFontSize(11);
        field.setColor(0, 0, 0);
        
        // Field-specific alignment
        if (fieldName.includes('Initials') || fieldName.includes('undefined_')) {
          field.setAlignment(1); // Center
        } else if (fieldName.startsWith('Text') && /^(79|80|81|82|83|84)$/.test(fieldName.replace('Text', ''))) {
          field.setAlignment(2); // Right for currency
        } else {
          field.setAlignment(0); // Left
        }
        
        console.log(`✓ SET FIELD: ${fieldName} = "${textValue}"`);
        return true;
      } catch (error) {
        console.log(`✗ FIELD ERROR: ${fieldName} - ${error.message}`);
        return false;
      }
    }
    
    // Helper function to safely set checkbox field (same as localhost)
    function setCheckBox(fieldName, isChecked) {
      try {
        const field = form.getCheckBox(fieldName);
        const boolValue = Boolean(isChecked);
        
        if (boolValue) {
          field.check();
        } else {
          field.uncheck();
        }
        
        console.log(`✓ SET CHECKBOX: ${fieldName} = ${boolValue}`);
        return true;
      } catch (error) {
        console.log(`✗ CHECKBOX ERROR: ${fieldName} - ${error.message}`);
        return false;
      }
    }
    
    // Helper function to validate date format
    function isValidDate(dateString) {
      const date = new Date(dateString);
      return date instanceof Date && !isNaN(date.getTime());
    }

    // Helper function to validate readable date format (e.g., "June 1, 2024")
    function isReadableDate(dateString) {
      // Check if it matches readable format like "June 1, 2024" or "July 1, 2024"
      const readablePattern = /^[A-Za-z]+ \d{1,2}, \d{4}$/;
      if (readablePattern.test(dateString)) {
        const date = new Date(dateString);
        return date instanceof Date && !isNaN(date.getTime());
      }
      return false;
    }
    
    // Processing statistics
    const stats = {
      textFields: 0,
      checkboxes: 0,
      errors: 0,
      skipped: 0
    };
    
    // Use the new FloridaTemplate mapping
    console.log('=== PROCESSING FORM FIELDS WITH FLORIDA TEMPLATE MAPPING ===');
    const mappedFields = mapFormDataToFloridaTemplate(formData);

    console.log(`Mapped ${Object.keys(mappedFields).length} fields for FloridaTemplate.pdf`);

    Object.entries(mappedFields).forEach(([fieldName, value]) => {
      try {
        if (typeof value === 'boolean') {
          if (setCheckBox(fieldName, value)) {
            stats.checkboxes++;
          } else {
            stats.errors++;
          }
        } else if (value !== null && value !== undefined && value !== '') {
          if (setTextField(fieldName, value)) {
            stats.textFields++;
          } else {
            stats.errors++;
          }
        } else {
          // Handle empty values
          stats.skipped++;
        }
      } catch (error) {
        console.error(`✗ PROCESSING ERROR: ${fieldName} - ${error.message}`);
        stats.errors++;
      }
    });
    
    console.log('=== PROCESSING COMPLETE ===');
    console.log('Statistics:', stats);
    
    // Verify critical fields are populated (updated for separate seller/buyer fields)
    const criticalFields = [
      { name: 'PARTIES', description: 'Seller name (Line 1)' },
      { name: 'and', description: 'Buyer name (Line 2)' },
      { name: 'a Street address city zip', description: 'Property address' },
      { name: 'County Florida Property Tax ID', description: 'County information' },
      { name: 'b Located in', description: 'Property Tax ID' },
      { name: 'Text79', description: 'Purchase price' },
      { name: 'Text80', description: 'Initial deposit' },
      { name: 'Date', description: 'Contract date' },
      { name: 'Buyers Initials', description: 'Buyer initials' },
      { name: 'Sellers Initials', description: 'Seller initials' }
    ];
    
    console.log('=== CRITICAL FIELD VERIFICATION ===');
    const missingFields = [];
    
    criticalFields.forEach(({ name, description }) => {
      try {
        const field = form.getTextField(name);
        const value = field.getText();
        if (value && value.trim()) {
          console.log(`✓ ${description}: "${value}"`);
        } else {
          console.log(`✗ ${description}: MISSING`);
          missingFields.push(description);
        }
      } catch (error) {
        console.log(`✗ ${description}: FIELD NOT FOUND`);
        missingFields.push(description);
      }
    });
    
    if (missingFields.length > 0) {
      console.warn('⚠️ Missing critical fields:', missingFields);
    } else {
      console.log('✓ All critical fields populated successfully');
    }
    
    console.log('=== PDF FORM FILLING COMPLETED ===');
    
    // Save and return the PDF
    const pdfBytes = await pdfDoc.save();
    console.log(`✓ Final PDF size: ${pdfBytes.length} bytes`);
    
    return {
      statusCode: 200,
      headers: {
        ...headers,
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'attachment; filename="Florida-Purchase-Agreement-Filled.pdf"',
      },
      body: Buffer.from(pdfBytes).toString('base64'),
      isBase64Encoded: true,
    };
    
  } catch (error) {
    console.error('=== PDF GENERATION ERROR ===');
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ 
        error: 'Failed to generate PDF',
        details: error.message,
        timestamp: new Date().toISOString()
      }),
    };
  }
};