{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:server": "node server/pdfGenerator.cjs", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@google/generative-ai": "^0.21.0", "cors": "^2.8.5", "express": "^5.1.0", "handlebars": "^4.7.8", "lucide-react": "^0.344.0", "pdf-lib": "^1.17.1", "puppeteer": "^22.15.0", "react": "^18.3.1", "react-dom": "^18.0.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}