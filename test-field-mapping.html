<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Field Mapping Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .section h2 {
            color: #2c3e50;
            margin-top: 0;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .field-mapping {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 10px;
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 3px;
            border-left: 4px solid #3498db;
        }
        .field-name {
            font-weight: bold;
            color: #2c3e50;
            font-family: monospace;
        }
        .field-value {
            color: #27ae60;
            font-family: monospace;
            word-break: break-all;
        }
        .test-button {
            background-color: #3498db;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            display: block;
            margin: 30px auto;
        }
        .test-button:hover {
            background-color: #2980b9;
        }
        .instructions {
            background-color: #e8f4fd;
            border: 1px solid #3498db;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .instructions h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .critical {
            border-left-color: #e74c3c;
        }
        .critical .field-name {
            color: #e74c3c;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 PDF Field Mapping Debug Tool</h1>
        
        <div class="instructions">
            <h3>How to Use This Tool:</h3>
            <ol>
                <li><strong>Review the field mappings below</strong> - these show exactly what values will be sent to the PDF</li>
                <li><strong>Generate a PDF</strong> using your form application</li>
                <li><strong>Compare the PDF output</strong> with the expected values shown here</li>
                <li><strong>If fields are empty in the PDF</strong>, the field names might be different than expected</li>
                <li><strong>Check browser console</strong> for "=== ALL PDF FIELD NAMES ===" section when generating PDF</li>
            </ol>
        </div>

        <div class="section">
            <h2>🎯 Critical Fields (Most Likely to Have Issues)</h2>
            
            <div class="field-mapping critical">
                <div class="field-name">PARTIES</div>
                <div class="field-value">JOHN DOE ("Seller"), and JANE SMITH ("Buyer")</div>
            </div>
            
            <div class="field-mapping critical">
                <div class="field-name">a Street address city zip</div>
                <div class="field-value">123 Main Street, Miami, FL 33101</div>
            </div>
            
            <div class="field-mapping critical">
                <div class="field-name">County Florida Property Tax ID</div>
                <div class="field-value">Miami-Dade, Florida</div>
            </div>
            
            <div class="field-mapping critical">
                <div class="field-name">b Located in</div>
                <div class="field-value">12-3456789</div>
            </div>
            
            <div class="field-mapping critical">
                <div class="field-name">Buyers Initials</div>
                <div class="field-value">JS</div>
            </div>
        </div>

        <div class="section">
            <h2>💰 Financial Fields</h2>
            
            <div class="field-mapping">
                <div class="field-name">Text79 (Purchase Price)</div>
                <div class="field-value">$500,000</div>
            </div>
            
            <div class="field-mapping">
                <div class="field-name">Text80 (Initial Deposit)</div>
                <div class="field-value">$25,000</div>
            </div>
            
            <div class="field-mapping">
                <div class="field-name">Text82 (Financing Amount)</div>
                <div class="field-value">$300,000</div>
            </div>
            
            <div class="field-mapping">
                <div class="field-name">Text84 (Balance to Close)</div>
                <div class="field-value">$170,000</div>
            </div>
        </div>

        <div class="section">
            <h2>📅 Date Fields</h2>
            
            <div class="field-mapping">
                <div class="field-name">Closing Date at the time established by the Closing Agent</div>
                <div class="field-value">July 1, 2024</div>
            </div>
            
            <div class="field-mapping">
                <div class="field-name">Date (Contract Date)</div>
                <div class="field-value">June 1, 2024</div>
            </div>
        </div>

        <div class="section">
            <h2>🏢 Escrow Information</h2>
            
            <div class="field-mapping">
                <div class="field-name">Escrow Agent Information Name</div>
                <div class="field-value">ABC Title Company</div>
            </div>
            
            <div class="field-mapping">
                <div class="field-name">Address</div>
                <div class="field-value">456 Title Street<br>Miami, FL 33101</div>
            </div>
        </div>

        <div class="section">
            <h2>✍️ Initials (All Pages)</h2>
            
            <div class="field-mapping">
                <div class="field-name">Sellers Initials</div>
                <div class="field-value">JD</div>
            </div>
            
            <div class="field-mapping">
                <div class="field-name">undefined_3 through undefined_15</div>
                <div class="field-value">JS (Buyer initials on all pages)</div>
            </div>
        </div>

        <button class="test-button" onclick="window.open('http://localhost:3000', '_blank')">
            🚀 Open Form Application to Test
        </button>

        <div class="instructions">
            <h3>🔧 Troubleshooting Steps:</h3>
            <p><strong>If PARTIES field is empty:</strong> The field name might be "Text1", "Text2", or something else entirely.</p>
            <p><strong>If address fields are empty:</strong> Try looking for field names like "Address", "Property Address", or "Text3".</p>
            <p><strong>If buyer initials are missing:</strong> The field might be named "Initials", "Buyer Initials", or "Text20".</p>
            <p><strong>If all fields are empty:</strong> There might be a fundamental issue with the PDF form structure or field access.</p>
        </div>
    </div>

    <script>
        console.log('🔍 PDF Field Mapping Debug Tool Loaded');
        console.log('Expected field mappings:');
        console.log('PARTIES: "JOHN DOE (\\"Seller\\"), and JANE SMITH (\\"Buyer\\")"');
        console.log('a Street address city zip: "123 Main Street, Miami, FL 33101"');
        console.log('County Florida Property Tax ID: "Miami-Dade, Florida"');
        console.log('b Located in: "12-3456789"');
        console.log('Text79: "$500,000"');
        console.log('Text80: "$25,000"');
        console.log('Text82: "$300,000"');
        console.log('Text84: "$170,000"');
        console.log('Buyers Initials: "JS"');
        console.log('Sellers Initials: "JD"');
    </script>
</body>
</html>
